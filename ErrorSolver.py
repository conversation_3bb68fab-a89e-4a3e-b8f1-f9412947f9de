# ErrorSolver.py - 错误处理智能体（参考AutoCompiler实现）
import json
import logging
import re
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor, Tool
from langchain_core.prompts import PromptTemplate
from tools import GitHubManager, GoogleSearchAgent
from config import (
    LLM3_BASE_URL, LLM3_MODEL, LLM3_API_KEY,
    LLM_CONFIGS, ERROR_SOLVER_PROMPT
)

class ErrorSolver:
    """错误处理智能体 - 采用ReAct框架的专业化错误诊断智能体"""
    
    def __init__(self, project_name: str, project_url: str = ""):
        self.project_name = project_name
        self.project_url = project_url
        self.logger = []

        # LLM配置（完全参考AutoCompiler的方式）
        config = LLM_CONFIGS["error_solver"]
        self.llm = ChatOpenAI(
            base_url=LLM3_BASE_URL,
            model=LLM3_MODEL,
            api_key=LLM3_API_KEY,
            temperature=config["temperature"],
            timeout=config["timeout"],
            max_tokens=config["max_tokens"]
        )

        # 初始化工具实例
        from config import PROXY_CONFIG
        proxy_url = f"http://{PROXY_CONFIG['http_proxy'].split('//')[1]}" if PROXY_CONFIG.get('http_proxy') else None

        self.github_manager = GitHubManager()
        self.google_search = GoogleSearchAgent(proxy=proxy_url)

    def solve(self, error_context: str) -> str:
        """
        Comprehensive analysis and resolution of C/C++ compilation errors.
        Uses GitHub Issues search and Google search to find similar problems and solutions.

        @param error_context: JSON string containing error details, project context, and compilation stage
        @return: JSON string with analyzed solutions and confidence ratings
        """
        try:
            # 构建内部工具集
            tools = [
                Tool(
                    name="GitHubIssuesSearcher",
                    description=self.github_manager.search_issues.__doc__,
                    func=lambda *args: self.github_manager.search_issues(*args) if len(args) >= 2 else self.github_manager.search_issues(self.project_url, "compilation error", 10)
                ),
                Tool(
                    name="GoogleSearcher",
                    description=self.google_search.search_compilation_solutions.__doc__,
                    func=self.google_search.search_compilation_solutions
                ),
                Tool(
                    name="QueryConstructor",
                    description=self.google_search.construct_search_query.__doc__,
                    func=self.google_search.construct_search_query
                ),
                Tool(
                    name="SolutionExtractor",
                    description=self.google_search.extract_solutions_from_results.__doc__,
                    func=self.google_search.extract_solutions_from_results
                ),
                Tool(
                    name="ErrorAnalyzer",
                    description=self.analyze_error_pattern.__doc__,
                    func=self.analyze_error_pattern
                ),
                Tool(
                    name="SolutionValidator",
                    description=self.validate_solution.__doc__,
                    func=self._validate_solution_wrapper
                )
            ]

            # 创建提示词模板
            prompt = PromptTemplate(
                template=ERROR_SOLVER_PROMPT,
                input_variables=["input", "agent_scratchpad", "tools", "tool_names", "project_name", "error_message"]
            )

            # 创建ReAct智能体
            agent = create_react_agent(llm=self.llm, tools=tools, prompt=prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                max_iterations=LLM_CONFIGS["error_solver"]["max_iterations"],
                verbose=True,
                handle_parsing_errors=True
            )

            # 解析错误上下文
            try:
                error_data = json.loads(error_context)
                error_message = error_data.get('error_message', error_context)
            except:
                error_message = error_context

            # 执行错误解决任务
            result = agent_executor.invoke({
                "input": error_context,
                "project_name": self.project_name,
                "error_message": error_message
            })

            # 记录日志
            self.logger.append([
                "solve",
                self.project_name,
                result["output"]
            ])

            return result["output"]
            
        except Exception as e:
            error_msg = f"Error solving failed: {str(e)}"
            self.logger.append([
                "solve",
                self.project_name,
                error_msg
            ])
            return json.dumps({
                "error_analysis": "Failed to analyze error",
                "solutions": [],
                "status": "failed",
                "error": error_msg
            })

    def analyze_error_pattern(self, error_message: str, compilation_stage: str = "unknown") -> str:
        """
        Analyze error patterns to categorize the type of compilation problem.
        Identifies common error categories: dependency missing, version conflicts, syntax errors, etc.

        @param error_message: Raw error message from compilation output
        @param compilation_stage: Stage where error occurred (dependency_install/source_compile/main_compile)
        @return: JSON string with error categorization and preliminary analysis
        """
        try:
            # 增强的错误类型分类
            error_categories = {
                "dependency_missing": [
                    "no such file", "cannot find", "not found", "missing",
                    "no package", "unable to locate package", "package not available",
                    "fatal error.*no such file", "could not find"
                ],
                "compile_error": [
                    "error:", "compilation terminated", "syntax error", "parse error",
                    "expected.*before", "undeclared", "was not declared",
                    "invalid conversion", "cannot convert"
                ],
                "linker_error": [
                    "undefined reference", "cannot find -l", "ld: error", "ld: cannot find",
                    "collect2: error", "relocation truncated", "multiple definition"
                ],
                "permission_denied": [
                    "permission denied", "access denied", "operation not permitted",
                    "cannot create directory", "cannot write"
                ],
                "timeout_error": [
                    "timeout", "killed", "terminated", "signal", "sigterm", "sigkill"
                ],
                "version_conflict": [
                    "version", "incompatible", "requires.*but", "conflicts with",
                    "unsupported.*version", "too old", "too new"
                ],
                "configuration_error": [
                    "configure: error", "cmake error", "makefile.*error",
                    "no rule to make", "target.*failed", "recipe.*failed"
                ],
                "line_ending_error": [
                    "bad interpreter", "/bin/sh\\^m", "no such file.*\\\\r",
                    "command not found.*\\\\r", "syntax error near unexpected token",
                    "\\^m", "\\\\r"
                ],
                "environment_error": [
                    "command not found", "no such command", "not in path",
                    "environment variable", "cflags", "ldflags", ": not found"
                ]
            }

            error_lower = error_message.lower()
            detected_categories = []

            # 支持多类别检测
            for category, keywords in error_categories.items():
                category_confidence = 0.0
                matched_keywords = []

                for keyword in keywords:
                    # 使用简单的字符串匹配而不是正则表达式
                    if keyword.lower() in error_lower:
                        matched_keywords.append(keyword)
                        category_confidence += 0.3  # 提高置信度

                if matched_keywords:
                    detected_categories.append({
                        "category": category,
                        "confidence": min(category_confidence, 1.0),
                        "matched_keywords": matched_keywords
                    })

            # 按置信度排序
            detected_categories.sort(key=lambda x: x["confidence"], reverse=True)

            # 提取关键错误信息
            key_terms = self._extract_key_terms(error_message)

            # 生成错误摘要
            error_summary = self._generate_error_summary(error_message, detected_categories)

            # 提取可能的解决方案关键词
            solution_keywords = self._extract_solution_keywords(detected_categories, key_terms)

            return json.dumps({
                "error_categories": detected_categories,
                "primary_category": detected_categories[0]["category"] if detected_categories else "unknown",
                "confidence": detected_categories[0]["confidence"] if detected_categories else 0.0,
                "key_terms": key_terms,
                "error_summary": error_summary,
                "solution_keywords": solution_keywords,
                "compilation_stage": compilation_stage,
                "analysis_status": "success"
            })

        except Exception as e:
            return json.dumps({
                "error_category": "unknown",
                "confidence": 0.0,
                "key_terms": [],
                "analysis_status": "failed",
                "error": str(e)
            })

    def _extract_key_terms(self, error_message: str) -> list:
        """从错误消息中提取关键术语"""
        key_terms = []

        # 提取可能的库名、文件名等
        patterns = [
            (r'lib\w+(?:\.so)?(?:\.\d+)*', 'library'),      # 库名
            (r'\w+\.h(?:pp)?', 'header'),                    # 头文件
            (r'\w+\.so(?:\.\d+)*', 'shared_library'),       # 动态库
            (r'\w+\.a', 'static_library'),                   # 静态库
            (r'\w+\.pc', 'pkg_config'),                      # pkg-config文件
            (r'[A-Z_]+_[A-Z_]+', 'macro'),                   # 宏定义
            (r'-l\w+', 'link_flag'),                         # 链接标志
            (r'[a-zA-Z_]\w*::\w+', 'namespace'),             # C++命名空间
            (r'[a-zA-Z_]\w*\(\)', 'function')               # 函数名
        ]

        for pattern, term_type in patterns:
            matches = re.findall(pattern, error_message)
            for match in matches:
                key_terms.append({
                    "term": match,
                    "type": term_type
                })

        # 去重并限制数量
        seen = set()
        unique_terms = []
        for term in key_terms:
            if term["term"] not in seen:
                seen.add(term["term"])
                unique_terms.append(term)
                if len(unique_terms) >= 10:  # 限制数量
                    break

        return unique_terms

    def _generate_error_summary(self, error_message: str, detected_categories: list) -> str:
        """生成错误摘要"""
        if not detected_categories:
            return "Unknown compilation error"

        primary_category = detected_categories[0]["category"]

        # 根据错误类别生成摘要
        summaries = {
            "dependency_missing": "Missing dependencies or files",
            "compile_error": "Source code compilation error",
            "linker_error": "Linking stage error",
            "permission_denied": "File system permission issue",
            "timeout_error": "Process timeout or termination",
            "version_conflict": "Version compatibility issue",
            "configuration_error": "Build configuration problem",
            "line_ending_error": "Line ending format issue",
            "environment_error": "Environment setup problem"
        }

        base_summary = summaries.get(primary_category, "Compilation error")

        # 添加关键信息
        error_lines = error_message.split('\n')
        key_line = ""
        for line in error_lines:
            if any(keyword in line.lower() for keyword in ["error:", "fatal:", "undefined reference"]):
                key_line = line.strip()[:100]  # 限制长度
                break

        if key_line:
            return f"{base_summary}: {key_line}"
        else:
            return base_summary

    def _extract_solution_keywords(self, detected_categories: list, key_terms: list) -> list:
        """提取解决方案搜索关键词"""
        keywords = []

        if not detected_categories:
            return ["compilation error", "build fix"]

        primary_category = detected_categories[0]["category"]

        # 基于错误类别的关键词
        category_keywords = {
            "dependency_missing": ["install", "dependency", "package", "missing"],
            "compile_error": ["compilation error", "syntax fix", "build error"],
            "linker_error": ["linker error", "undefined reference", "linking"],
            "permission_denied": ["permission", "access", "chmod"],
            "timeout_error": ["timeout", "memory", "resource"],
            "version_conflict": ["version", "compatibility", "upgrade"],
            "configuration_error": ["configure", "cmake", "makefile"],
            "line_ending_error": ["line ending", "dos2unix", "CRLF"],
            "environment_error": ["environment", "PATH", "variable"]
        }

        keywords.extend(category_keywords.get(primary_category, []))

        # 添加关键术语
        for term in key_terms[:3]:  # 只取前3个
            if term["type"] in ["library", "header"]:
                keywords.append(term["term"])

        return keywords[:8]  # 限制关键词数量

    def _validate_solution_wrapper(self, input_str: str) -> str:
        """
        Wrapper function for SolutionValidator tool.
        Parses JSON input and calls validate_solution with proper parameters.
        """
        try:
            import json
            input_data = json.loads(input_str)
            proposed_solution = input_data.get("proposed_solution", "")
            error_context = input_data.get("error_context", "")
            return self.validate_solution(proposed_solution, error_context)
        except json.JSONDecodeError:
            # 如果不是JSON格式，尝试直接使用输入作为solution
            return self.validate_solution(input_str, "unknown_context")
        except Exception as e:
            return json.dumps({
                "validation_score": 0.0,
                "issues": [f"Validation wrapper error: {str(e)}"],
                "status": "failed"
            })

    def validate_solution(self, proposed_solution: str, error_context: str) -> str:
        """
        Validate the feasibility and safety of proposed solutions.
        Checks for potential side effects and compatibility with the target environment.

        @param proposed_solution: Solution commands or instructions to validate
        @param error_context: Original error context for validation reference
        @return: JSON string with validation results and risk assessment
        """
        try:
            solution_data = json.loads(proposed_solution) if isinstance(proposed_solution, str) else proposed_solution

            # 基本安全检查
            risk_level = "low"
            warnings = []

            commands = solution_data.get("commands", [])
            for command in commands:
                if isinstance(command, str):
                    # 检查危险命令
                    dangerous_patterns = ["rm -rf", "sudo rm", "format", "mkfs"]
                    for pattern in dangerous_patterns:
                        if pattern in command.lower():
                            risk_level = "high"
                            warnings.append(f"Dangerous command detected: {pattern}")

                    # 检查系统级修改
                    system_patterns = ["sudo", "chmod 777", "chown"]
                    for pattern in system_patterns:
                        if pattern in command.lower():
                            if risk_level == "low":
                                risk_level = "medium"
                            warnings.append(f"System-level modification: {pattern}")

            return json.dumps({
                "validation_status": "success",
                "risk_level": risk_level,
                "warnings": warnings,
                "is_safe": risk_level != "high",
                "recommendations": self._generate_safety_recommendations(risk_level, warnings)
            })

        except Exception as e:
            return json.dumps({
                "validation_status": "failed",
                "risk_level": "unknown",
                "warnings": [],
                "is_safe": False,
                "error": str(e)
            })

    def _generate_safety_recommendations(self, risk_level: str, warnings: list) -> list:
        """生成安全建议"""
        recommendations = []

        if risk_level == "high":
            recommendations.append("Review commands carefully before execution")
            recommendations.append("Consider running in isolated environment")
        elif risk_level == "medium":
            recommendations.append("Monitor system changes during execution")
            recommendations.append("Have rollback plan ready")

        if warnings:
            recommendations.append("Address specific warnings before proceeding")

        return recommendations

    def get_error_logs(self) -> list:
        """获取错误处理日志"""
        return self.logger.copy()
