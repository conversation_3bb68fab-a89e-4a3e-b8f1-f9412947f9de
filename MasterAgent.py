# MasterAgent.py - 主控智能体（参考AutoCompiler实现）
import json
import logging
import os
import time
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor, Tool
from langchain_core.prompts import PromptTemplate
from tools import InteractiveDockerShell, GitHubManager, DependencyTracker
from ProjectAnalyzer import ProjectAnalyzer
from ErrorSolver import ErrorSolver
from config import (
    LLM1_BASE_URL, LLM1_MODEL, LLM1_API_KEY,
    LLM_CONFIGS, MASTER_AGENT_PROMPT, DEFAULT_UBUNTU_VERSION
)

class MasterAgent:
    """主控智能体 - 整体编译流程的调度和决策"""
    
    def __init__(self, project_name: str, project_url: str, local_path: str):
        self.project_name = project_name
        self.project_url = project_url
        self.local_path = local_path
        self.logger = []
        
        # LLM配置
        config = LLM_CONFIGS["master_agent"]
        self.llm = ChatOpenAI(
            base_url=LLM1_BASE_URL,
            model=LLM1_MODEL,
            api_key=LLM1_API_KEY,
            temperature=config["temperature"],
            timeout=config["timeout"],
            max_tokens=config["max_tokens"]
        )
        
        # 初始化工具实例
        self.docker_shell = None
        self.github_manager = GitHubManager()
        self.dependency_tracker = DependencyTracker()
        self.project_analyzer = None
        self.error_solver = None

    def compile_project(self) -> str:
        """
        Execute complete project compilation workflow with error handling and retry mechanism.

        @return: Compilation result (COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN)
        """
        master_retry_count = 0
        max_master_retries = 3
        max_error_solver_retries = 3
        compilation_result = "COMPILATION-FAIL"  # 初始化默认值

        try:
            # 初始化智能体实例
            self.project_analyzer = ProjectAnalyzer(self.local_path, self.project_name)
            self.error_solver = ErrorSolver(self.project_name, self.project_url)

            # 构建工具集
            tools = self._build_tools()

            # 创建提示词模板
            prompt = PromptTemplate(
                template=MASTER_AGENT_PROMPT,
                input_variables=["input", "agent_scratchpad", "tools", "tool_names", "project_name"]
            )

            # 创建ReAct智能体
            agent = create_react_agent(llm=self.llm, tools=tools, prompt=prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                max_iterations=LLM_CONFIGS["master_agent"]["max_iterations"],
                verbose=True,
                handle_parsing_errors=True
            )

            # 主控智能体重试循环
            while master_retry_count < max_master_retries:
                try:
                    # 执行编译任务
                    compilation_prompt = f"Compile C/C++ project {self.project_name} located at {self.local_path}"
                    result = agent_executor.invoke({
                        "input": compilation_prompt,
                        "project_name": self.project_name
                    })

                    compilation_result = result["output"]

                    # 检查编译结果 - 支持多种成功表达方式
                    success_indicators = [
                        "COMPILATION-SUCCESS",
                        "compilation has been successful",
                        "compilation successful",
                        "build successful",
                        "successfully compiled",
                        "Perfect! The compilation has been successful"
                    ]

                    if any(indicator.lower() in compilation_result.lower() for indicator in success_indicators):
                        self.logger.append([
                            "compile_project",
                            self.project_name,
                            f"Success after {master_retry_count + 1} attempts"
                        ])
                        return "COMPILATION-SUCCESS"  # 返回标准化的成功标识

                    # 检查是否是可修复的编译错误
                    if self._is_recoverable_error(compilation_result):
                        master_retry_count += 1
                        if master_retry_count < max_master_retries:
                            self.logger.append([
                                "compile_project_retry",
                                self.project_name,
                                f"Master agent retry {master_retry_count}/{max_master_retries}"
                            ])
                            continue
                        else:
                            # 主控智能体重试次数用完，调用错误处理智能体
                            break
                    else:
                        # 不可修复的错误，直接返回失败
                        return compilation_result

                except Exception as e:
                    error_str = str(e)

                    # 区分API错误和编译错误
                    if any(api_error in error_str for api_error in ["503", "500", "502", "504", "timeout", "connection"]):
                        # API服务错误，直接重试，不调用错误处理智能体
                        master_retry_count += 1
                        error_msg = f"API service error on attempt {master_retry_count}: {error_str}"
                        self.logger.append([
                            "api_service_error",
                            self.project_name,
                            error_msg
                        ])

                        if master_retry_count >= max_master_retries:
                            # API服务持续不可用，直接返回失败
                            final_error = f"API service unavailable after {max_master_retries} attempts"
                            self.logger.append([
                                "api_service_final_fail",
                                self.project_name,
                                final_error
                            ])
                            return "COMPILATION-FAIL"
                        continue
                    else:
                        # 其他类型的异常，可能是编译相关的
                        master_retry_count += 1
                        error_msg = f"Master agent attempt {master_retry_count} failed: {error_str}"
                        self.logger.append([
                            "compile_project_error",
                            self.project_name,
                            error_msg
                        ])

                        if master_retry_count >= max_master_retries:
                            break
                        continue

            # 错误处理智能体重试循环
            return self._handle_with_error_solver(compilation_result, max_error_solver_retries)

        except Exception as e:
            error_msg = f"Master agent compilation failed: {str(e)}"
            self.logger.append([
                "compile_project",
                self.project_name,
                error_msg
            ])
            return "COMPILATION-FAIL"
        finally:
            # 清理资源
            if self.docker_shell:
                self.docker_shell.close()

    def _build_tools(self) -> list:
        """构建工具集"""
        tools = []
        
        # Docker Shell工具
        def create_docker_shell(local_path: str = None) -> str:
            """
            Create Docker container and establish SSH connection for compilation.

            @param local_path: Path to project directory (optional, uses self.local_path if not provided)
            @return: Status message
            """
            try:
                if self.docker_shell:
                    self.docker_shell.close()

                target_path = local_path or self.local_path
                self.docker_shell = InteractiveDockerShell(
                    local_path=target_path,
                    ubuntu_version=DEFAULT_UBUNTU_VERSION
                )
                return f"Docker container created successfully with Ubuntu {DEFAULT_UBUNTU_VERSION} for path: {target_path}"
            except Exception as e:
                return f"Failed to create Docker container: {str(e)}"
        
        def execute_shell_command(command: str) -> str:
            """
            Execute command in Docker container via SSH with intelligent error handling.

            @param command: Shell command to execute
            @return: Command output
            """
            if not self.docker_shell:
                return "Error: No Docker container available. Create container first."

            try:
                # 特殊处理Linux内核编译命令
                if "menuconfig" in command:
                    # 替换交互式配置为默认配置
                    command = command.replace("menuconfig", "defconfig")
                    self.logger.append([
                        "command_auto_fix",
                        self.project_name,
                        f"Auto-fixed menuconfig to defconfig for non-interactive environment"
                    ])

                result = self.docker_shell.execute_command(command)

                # 检查常见错误并自动修复
                if "Error opening terminal: unknown" in result:
                    # 终端错误，尝试使用defconfig
                    if "make" in command:
                        fixed_command = "make defconfig && make -j$(nproc)"
                        self.logger.append([
                            "command_auto_fix",
                            self.project_name,
                            f"Auto-fixing terminal error with: {fixed_command}"
                        ])
                        result = self.docker_shell.execute_command(fixed_command)

                return result
            except Exception as e:
                return f"Command execution failed: {str(e)}"
        
        def switch_ubuntu_version(target_version: str) -> str:
            """
            Switch to different Ubuntu version for compatibility issues.
            
            @param target_version: Target Ubuntu version (18.04/20.04/22.04)
            @return: Switch result message
            """
            if not self.docker_shell:
                return "Error: No Docker container available."
            
            try:
                return self.docker_shell.switch_ubuntu_version(target_version)
            except Exception as e:
                return f"Version switch failed: {str(e)}"
        
        def create_git_snapshot() -> str:
            """
            Create git snapshot for artifact detection.
            
            @return: Status message
            """
            if not self.docker_shell:
                return "Error: No Docker container available."
            
            try:
                return self.docker_shell.create_git_snapshot("/work")
            except Exception as e:
                return f"Git snapshot creation failed: {str(e)}"
        
        def detect_artifacts() -> str:
            """
            Detect compilation artifacts after build.

            @return: JSON string with detected artifacts
            """
            if not self.docker_shell:
                return json.dumps({"artifacts": [], "status": "no_container"})

            try:
                return self.docker_shell.detect_compilation_artifacts("/work")
            except Exception as e:
                return json.dumps({"artifacts": [], "status": "failed", "error": str(e)})

        def fix_line_endings(*args) -> str:
            """
            Fix line ending issues in shell scripts (Windows CRLF to Unix LF).

            @return: Status message
            """
            if not self.docker_shell:
                return "Error: No Docker container available."

            try:
                # 在容器中安装dos2unix工具并修复configure脚本
                commands = [
                    "apt-get update",
                    "apt-get install -y dos2unix",
                    "find /work -name 'configure' -o -name '*.sh' -o -name 'autogen*' -o -name 'bootstrap' | xargs dos2unix",
                    "chmod +x /work/configure"
                ]

                results = []
                for cmd in commands:
                    result = self.docker_shell.execute_command(cmd)
                    results.append(f"Command: {cmd}\nResult: {result[:200]}...")

                return "Line endings fixed successfully:\n" + "\n".join(results)
            except Exception as e:
                return f"Failed to fix line endings: {str(e)}"
        
        # 添加工具到列表
        tools.extend([
            Tool(
                name="CreateDockerShell",
                description=create_docker_shell.__doc__,
                func=create_docker_shell
            ),
            Tool(
                name="Shell",
                description=execute_shell_command.__doc__,
                func=execute_shell_command
            ),
            Tool(
                name="VersionSwitcher",
                description=switch_ubuntu_version.__doc__,
                func=switch_ubuntu_version
            ),
            Tool(
                name="GitSnapshot",
                description=create_git_snapshot.__doc__,
                func=create_git_snapshot
            ),
            Tool(
                name="ArtifactDetector",
                description=detect_artifacts.__doc__,
                func=detect_artifacts
            ),
            Tool(
                name="ProjectAnalyzer",
                description=self._get_project_analyzer_description(),
                func=self._call_project_analyzer
            ),
            Tool(
                name="ErrorSolver",
                description=self._get_error_solver_description(),
                func=self._call_error_solver
            ),
            Tool(
                name="GitHubCloner",
                description=self.github_manager.clone_project.__doc__,
                func=self.github_manager.clone_project
            ),
            Tool(
                name="ProjectCopyManager",
                description=self.github_manager.create_project_copy.__doc__,
                func=self.github_manager.create_project_copy
            ),
            Tool(
                name="LineEndingFixer",
                description=fix_line_endings.__doc__,
                func=fix_line_endings
            )
        ])
        
        return tools

    def _is_recoverable_error(self, compilation_result: str) -> bool:
        """
        检查编译错误是否可修复
        根据文档要求，识别常见的可修复编译错误类型
        """
        error_indicators = [
            "error:", "Error:", "ERROR:",
            "fatal error:", "Fatal error:", "FATAL ERROR:",
            "make:", "Make:", "MAKE:",
            "undefined reference", "cannot find",
            "No such file", "command not found",
            "Permission denied", "permission denied",
            "menuconfig", "terminal: unknown",
            "dependency", "missing", "not found"
        ]

        compilation_lower = compilation_result.lower()

        # 检查是否包含错误指示符
        has_error = any(indicator.lower() in compilation_lower for indicator in error_indicators)

        # 排除明确的成功标识
        has_success = any(success_indicator in compilation_lower for success_indicator in [
            "compilation-success", "build successful", "success"
        ])

        return has_error and not has_success

    def _handle_with_error_solver(self, last_compilation_result: str, max_retries: int) -> str:
        """
        使用错误处理智能体处理编译错误
        严格按照文档要求实现错误信息标准化处理
        """
        error_solver_retry_count = 0

        while error_solver_retry_count < max_retries:
            try:
                # 构建标准化错误上下文（按照文档要求）
                error_context = {
                    "error_type": self._classify_error_type(last_compilation_result),
                    "error_message": last_compilation_result,
                    "error_context": f"Project: {self.project_name}, Path: {self.local_path}",
                    "compilation_stage": "main_compile",
                    "suggested_solutions": [],
                    "retry_count": error_solver_retry_count + 1,
                    "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
                }

                # 调用错误处理智能体
                solution_result = self.error_solver.solve(json.dumps(error_context))

                # 解析解决方案
                try:
                    solution_data = json.loads(solution_result)
                    if solution_data.get("success", False) and solution_data.get("solutions"):
                        # 执行解决方案
                        for solution in solution_data["solutions"]:
                            if self.docker_shell:
                                self.docker_shell.execute_command(solution)

                        # 重新尝试编译
                        retry_result = self._retry_compilation()
                        if "COMPILATION-SUCCESS" in retry_result:
                            self.logger.append([
                                "error_solver_success",
                                self.project_name,
                                f"Fixed after {error_solver_retry_count + 1} error solver attempts"
                            ])
                            return retry_result

                        # 更新错误信息用于下次重试
                        last_compilation_result = retry_result

                except json.JSONDecodeError:
                    pass

                error_solver_retry_count += 1
                self.logger.append([
                    "error_solver_retry",
                    self.project_name,
                    f"Error solver retry {error_solver_retry_count}/{max_retries}"
                ])

            except Exception as e:
                error_solver_retry_count += 1
                self.logger.append([
                    "error_solver_error",
                    self.project_name,
                    f"Error solver attempt {error_solver_retry_count} failed: {str(e)}"
                ])

        # 所有错误处理智能体重试都失败
        final_error = f"Error solver failed after {max_retries} attempts"
        self.logger.append([
            "error_solver_final_fail",
            self.project_name,
            final_error
        ])
        return "COMPILATION-FAIL"

    def _classify_error_type(self, error_message: str) -> str:
        """
        根据文档要求分类错误类型
        """
        error_lower = error_message.lower()

        if any(keyword in error_lower for keyword in ["no such file", "cannot find", "missing", "not found"]):
            return "dependency_missing"
        elif any(keyword in error_lower for keyword in ["permission denied", "access denied"]):
            return "permission_denied"
        elif any(keyword in error_lower for keyword in ["timeout", "timed out"]):
            return "timeout_error"
        else:
            return "compile_error"

    def _retry_compilation(self) -> str:
        """
        重新尝试编译
        """
        if self.docker_shell:
            # 尝试基本的编译命令
            result = self.docker_shell.execute_command("make -j$(nproc)")
            if "error" not in result.lower():
                return "COMPILATION-SUCCESS"
            return result
        return "COMPILATION-FAIL"

    def _get_project_analyzer_description(self) -> str:
        """获取项目分析器描述"""
        return """Analyze C/C++ project structure, dependencies and build instructions.
        Returns JSON with dependencies list and build commands."""

    def _call_project_analyzer(self, *args) -> str:
        """调用项目分析器"""
        if not self.project_analyzer:
            return json.dumps({"error": "ProjectAnalyzer not initialized"})
        
        try:
            return self.project_analyzer.analyze_comprehensive()
        except Exception as e:
            return json.dumps({"error": f"Project analysis failed: {str(e)}"})

    def _get_error_solver_description(self) -> str:
        """获取错误解决器描述"""
        return """Solve compilation errors using GitHub Issues and Google search.
        Input: JSON string with error context. Returns solutions with commands."""

    def _call_error_solver(self, error_context: str) -> str:
        """调用错误解决器"""
        if not self.error_solver:
            return json.dumps({"error": "ErrorSolver not initialized"})
        
        try:
            return self.error_solver.solve(error_context)
        except Exception as e:
            return json.dumps({"error": f"Error solving failed: {str(e)}"})

    def get_compilation_logs(self) -> list:
        """获取编译日志"""
        all_logs = self.logger.copy()
        
        if self.project_analyzer:
            all_logs.extend(self.project_analyzer.get_analysis_logs())
        
        if self.error_solver:
            all_logs.extend(self.error_solver.get_error_logs())
        
        if self.docker_shell:
            all_logs.extend(self.docker_shell.logger)
        
        return all_logs
