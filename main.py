#!/usr/bin/env python3
# main.py - 自动化编译系统主程序
import os
import sys
import json
import logging
import argparse
from urllib.parse import urlparse
from MasterAgent import MasterAgent
from tools import GitHubManager
from config import DATASET_BASE_PATH, PROXY_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('autocompile.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class AutoCompileSystem:
    """自动化编译系统主类"""
    
    def __init__(self, dataset_path: str = None):
        self.dataset_path = dataset_path or DATASET_BASE_PATH
        # 使用代理配置初始化GitHubManager
        proxy = PROXY_CONFIG.get("git_proxy") if PROXY_CONFIG else None
        self.github_manager = GitHubManager(proxy=proxy)
        self.compilation_results = []
        
        # 确保数据集目录存在
        os.makedirs(self.dataset_path, exist_ok=True)
        os.makedirs(os.path.join(self.dataset_path, "projects"), exist_ok=True)
        os.makedirs(os.path.join(self.dataset_path, "binaries"), exist_ok=True)
        os.makedirs(os.path.join(self.dataset_path, "logs"), exist_ok=True)

    def process_projects_from_file(self, projects_file: str) -> dict:
        """
        从文件读取项目URL列表并逐个处理
        
        @param projects_file: 包含项目URL的文件路径
        @return: 处理结果统计
        """
        if not os.path.exists(projects_file):
            logging.error(f"Projects file not found: {projects_file}")
            return {"success": 0, "failed": 0, "total": 0}
        
        try:
            with open(projects_file, 'r', encoding='utf-8') as f:
                project_urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            
            logging.info(f"Found {len(project_urls)} projects to compile")
            
            results = {"success": 0, "failed": 0, "total": len(project_urls)}
            
            for i, project_url in enumerate(project_urls, 1):
                logging.info(f"Processing project {i}/{len(project_urls)}: {project_url}")
                
                try:
                    result = self.compile_single_project(project_url)
                    if result["status"] == "COMPILATION-SUCCESS":
                        results["success"] += 1
                    else:
                        results["failed"] += 1
                    
                    self.compilation_results.append(result)
                    
                except Exception as e:
                    logging.error(f"Failed to process project {project_url}: {str(e)}")
                    results["failed"] += 1
                    self.compilation_results.append({
                        "project_url": project_url,
                        "status": "COMPILATION-FAIL",
                        "error": str(e)
                    })
            
            # 保存结果报告
            self.save_compilation_report(results)
            
            return results
            
        except Exception as e:
            logging.error(f"Error processing projects file: {str(e)}")
            return {"success": 0, "failed": 0, "total": 0, "error": str(e)}

    def compile_single_project(self, project_url: str) -> dict:
        """
        编译单个项目
        
        @param project_url: 项目GitHub URL
        @return: 编译结果字典
        """
        project_name = self.extract_project_name(project_url)
        project_dir = os.path.join(self.dataset_path, "projects", project_name)
        
        logging.info(f"Starting compilation of project: {project_name}")
        
        try:
            # 1. 检查项目是否已存在
            if os.path.exists(project_dir):
                logging.info(f"Project {project_name} already exists, using existing copy")
            else:
                # 下载项目
                clone_result = self.github_manager.clone_project(project_url, project_dir)
                if "Error:" in clone_result:
                    return {
                        "project_name": project_name,
                        "project_url": project_url,
                        "status": "COMPILATION-FAIL",
                        "stage": "download",
                        "error": clone_result
                    }
            
            # 2. 创建项目副本
            copy_result = self.github_manager.create_project_copy(project_dir)
            if "Error:" in copy_result:
                return {
                    "project_name": project_name,
                    "project_url": project_url,
                    "status": "COMPILATION-FAIL",
                    "stage": "copy",
                    "error": copy_result
                }
            
            copy_path = copy_result
            
            # 3. 初始化主控智能体并执行编译
            master_agent = MasterAgent(project_name, project_url, copy_path)
            compilation_result = master_agent.compile_project()
            
            # 4. 保存编译日志
            self.save_project_logs(project_name, master_agent.get_compilation_logs())
            
            # 5. 处理编译产物（如果成功）
            artifacts_info = {}
            if "COMPILATION-SUCCESS" in compilation_result:
                artifacts_info = self.handle_compilation_artifacts(project_name, copy_path, master_agent)
            
            return {
                "project_name": project_name,
                "project_url": project_url,
                "status": compilation_result,
                "copy_path": copy_path,
                "artifacts": artifacts_info,
                "logs_saved": True
            }
            
        except Exception as e:
            logging.error(f"Compilation failed for {project_name}: {str(e)}")
            return {
                "project_name": project_name,
                "project_url": project_url,
                "status": "COMPILATION-FAIL",
                "stage": "compilation",
                "error": str(e)
            }

    def extract_project_name(self, project_url: str) -> str:
        """从URL提取项目名称"""
        try:
            parsed = urlparse(project_url)
            path_parts = parsed.path.strip('/').split('/')
            if len(path_parts) >= 2:
                return f"{path_parts[-2]}_{path_parts[-1].replace('.git', '')}"
            else:
                return path_parts[-1].replace('.git', '') if path_parts else "unknown_project"
        except:
            return "unknown_project"

    def handle_compilation_artifacts(self, project_name: str, project_path: str, master_agent) -> dict:
        """处理编译产物"""
        try:
            artifacts_dir = os.path.join(self.dataset_path, "binaries", project_name)
            os.makedirs(artifacts_dir, exist_ok=True)

            # 使用MasterAgent的docker_shell来收集编译产物
            if hasattr(master_agent, 'docker_shell') and master_agent.docker_shell:
                docker_shell = master_agent.docker_shell

                # 收集编译产物到主机
                collection_result = docker_shell.collect_artifacts_to_host("/work", artifacts_dir)

                if collection_result["status"] == "success":
                    logging.info(f"Successfully collected {collection_result['collected_count']} artifacts for {project_name}")
                    return {
                        "artifacts_dir": artifacts_dir,
                        "status": "artifacts_collected",
                        "collected_count": collection_result["collected_count"],
                        "failed_count": collection_result["failed_count"],
                        "metadata_file": collection_result.get("metadata_file"),
                        "artifacts": collection_result.get("collected_artifacts", [])
                    }
                else:
                    logging.warning(f"Failed to collect artifacts for {project_name}: {collection_result.get('error', 'Unknown error')}")
                    return {
                        "artifacts_dir": artifacts_dir,
                        "status": "artifacts_collection_failed",
                        "error": collection_result.get("error", "Unknown error")
                    }
            else:
                logging.warning(f"No docker shell available for artifact collection: {project_name}")
                return {
                    "artifacts_dir": artifacts_dir,
                    "status": "artifacts_no_docker_shell"
                }

        except Exception as e:
            logging.error(f"Failed to handle artifacts for {project_name}: {str(e)}")
            return {
                "status": "artifacts_failed",
                "error": str(e)
            }

    def save_project_logs(self, project_name: str, logs: list):
        """保存项目编译日志"""
        try:
            log_file = os.path.join(self.dataset_path, "logs", f"{project_name}_compilation.json")
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, indent=2, ensure_ascii=False)
            logging.info(f"Logs saved for project {project_name}")
        except Exception as e:
            logging.error(f"Failed to save logs for {project_name}: {str(e)}")

    def save_compilation_report(self, results: dict):
        """保存编译结果报告"""
        try:
            report = {
                "summary": results,
                "detailed_results": self.compilation_results,
                "timestamp": logging.Formatter().formatTime(logging.LogRecord("", 0, "", 0, "", (), None))
            }
            
            report_file = os.path.join(self.dataset_path, "compilation_report.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Compilation report saved to {report_file}")
            logging.info(f"Summary: {results['success']} successful, {results['failed']} failed, {results['total']} total")
            
        except Exception as e:
            logging.error(f"Failed to save compilation report: {str(e)}")


def main():
    """主程序入口"""
    parser = argparse.ArgumentParser(description="Automated C/C++ Project Compilation System")
    parser.add_argument("--projects-file", "-f", default="projects_url.txt",
                       help="File containing project URLs (default: projects_url.txt)")
    parser.add_argument("--dataset-path", "-d", default=None,
                       help="Dataset output path (default: from config)")
    parser.add_argument("--single-project", "-s", default=None,
                       help="Compile single project by URL")
    
    args = parser.parse_args()
    
    # 初始化系统
    system = AutoCompileSystem(args.dataset_path)
    
    try:
        if args.single_project:
            # 编译单个项目
            logging.info(f"Compiling single project: {args.single_project}")
            result = system.compile_single_project(args.single_project)
            print(json.dumps(result, indent=2))
        else:
            # 批量编译项目
            logging.info(f"Starting batch compilation from file: {args.projects_file}")
            results = system.process_projects_from_file(args.projects_file)
            print(f"Compilation completed: {results}")
            
    except KeyboardInterrupt:
        logging.info("Compilation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logging.error(f"System error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
