# ProjectAnalyzer.py - 项目分析智能体（参考AutoCompiler实现）
import json
import logging
from langchain_openai import ChatOpenAI
from langchain.agents import create_react_agent, AgentExecutor, Tool
from langchain_core.prompts import PromptTemplate
from tools import DependencyScanner, DocumentAnalyzer
from config import (
    LLM2_BASE_URL, LLM2_MODEL, LLM2_API_KEY, 
    LLM_CONFIGS, PROJECT_ANALYZER_PROMPT
)

class ProjectAnalyzer:
    """项目分析智能体 - 采用ReAct框架的独立智能体"""
    
    def __init__(self, project_path: str, project_name: str):
        self.project_path = project_path
        self.project_name = project_name
        self.logger = []

        # LLM配置（完全参考AutoCompiler的方式）
        config = LLM_CONFIGS["project_analyzer"]
        self.llm = ChatOpenAI(
            base_url=LLM2_BASE_URL,
            model=LLM2_MODEL,
            api_key=LLM2_API_KEY,
            temperature=config["temperature"],
            timeout=config["timeout"],
            max_tokens=config["max_tokens"]
        )

        # 初始化工具实例 - 使用简化方案
        self.dependency_scanner = DependencyScanner()
        self.directory_analyzer = DirectoryAnalyzer(project_path)
        self.document_analyzer = DocumentAnalyzer(project_path, project_name)  # 保留作为备用

    def analyze_comprehensive(self, *args) -> str:
        """
        Simplified comprehensive analysis using tree command and intelligent file selection.
        Uses LLM to analyze directory structure and intelligently select files to read.

        @param: this function takes no parameter, analyzes the project at self.project_path
        @return: Structured string containing dependencies and build commands
        """
        try:
            # 简化工具集 - 使用新的DirectoryAnalyzer
            tools = [
                Tool(
                    name="TreeAnalyzer",
                    description=self.directory_analyzer.get_directory_structure.__doc__,
                    func=self.directory_analyzer.get_directory_structure
                ),
                Tool(
                    name="FileSuggester",
                    description=self.directory_analyzer.suggest_key_files.__doc__,
                    func=self.directory_analyzer.suggest_key_files
                ),
                Tool(
                    name="FileReader",
                    description=self.directory_analyzer.read_selected_files.__doc__,
                    func=self.directory_analyzer.read_selected_files
                ),
                Tool(
                    name="DependencyScanner",
                    description=self.dependency_scanner.scan_dependencies.__doc__,
                    func=self.dependency_scanner.scan_dependencies
                ),
                # 保留DocumentAnalyzer作为备用
                Tool(
                    name="DocumentAnalyzer",
                    description=self.document_analyzer.analyze_documents.__doc__,
                    func=self.document_analyzer.analyze_documents
                )
            ]

            # 创建提示词模板
            prompt = PromptTemplate(
                template=PROJECT_ANALYZER_PROMPT,
                input_variables=["input", "agent_scratchpad", "tools", "tool_names", "project_name", "project_path"]
            )

            # 创建ReAct智能体
            agent = create_react_agent(llm=self.llm, tools=tools, prompt=prompt)
            agent_executor = AgentExecutor(
                agent=agent,
                tools=tools,
                max_iterations=LLM_CONFIGS["project_analyzer"]["max_iterations"],
                verbose=True,
                handle_parsing_errors=True
            )

            # 执行分析任务（完全参考AutoCompiler的调用方式）
            analysis_prompt = f"Analyze C/C++ project at {self.project_path}, extract dependencies and build instructions"
            answer = agent_executor.invoke({
                "input": analysis_prompt,
                "project_name": self.project_name,
                "project_path": self.project_path
            })

            # 记录日志（参考AutoCompiler的日志格式）
            self.logger.append([
                "analyze_comprehensive",
                self.project_name,
                answer['output']
            ])

            return answer['output']  # 统一返回answer['output']
            
        except Exception as e:
            error_msg = f"Project analysis failed: {str(e)}"
            self.logger.append([
                "analyze_comprehensive",
                self.project_name,
                error_msg
            ])
            return json.dumps({
                "dependencies": [],
                "build_commands": [],
                "build_system": "unknown",
                "status": "failed",
                "error": error_msg
            })

    def resolve_dependency_conflicts(self, ccscanner_deps: str, doc_deps: str) -> str:
        """
        Resolve conflicts between ccscanner results and document analysis results.
        Priority: Document analysis > ccscanner results.

        @param ccscanner_deps: JSON string of ccscanner dependency results
        @param doc_deps: JSON string of document analysis dependency results
        @return: JSON string with resolved final dependency list
        """
        try:
            cc_data = json.loads(ccscanner_deps) if ccscanner_deps else {"dependencies": []}
            doc_data = json.loads(doc_deps) if doc_deps else {"dependencies": []}

            final_deps = []

            # 优先采用文档分析结果
            for doc_dep in doc_data.get("dependencies", []):
                final_deps.append({
                    **doc_dep,
                    "source": "document_analysis",
                    "priority": "high"
                })

            # 添加ccscanner中文档分析未包含的依赖
            doc_names = {dep['name'] for dep in doc_data.get("dependencies", [])}
            for cc_dep in cc_data.get("dependencies", []):
                if cc_dep['name'] not in doc_names:
                    final_deps.append({
                        **cc_dep,
                        "source": "ccscanner_supplementary",
                        "priority": "medium"
                    })

            return json.dumps({
                "dependencies": final_deps,
                "resolution_status": "success",
                "total_count": len(final_deps)
            })

        except Exception as e:
            return json.dumps({
                "dependencies": [],
                "resolution_status": "failed",
                "error": str(e)
            })

    def get_analysis_logs(self) -> list:
        """获取分析日志"""
        return self.logger.copy()
