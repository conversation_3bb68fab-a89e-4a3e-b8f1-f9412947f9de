# 关键文件识别策略实现

## 🎯 问题分析

您提出的问题非常重要：**智能文件识别策略应该明确写在提示词中**，让大模型知道应该优先关注哪些类型的文件。

## 🔧 实现的改进

### 1. 提示词中明确关键文件类型

在`config.py`的`PROJECT_ANALYZER_PROMPT`中添加了详细的文件优先级列表：

```python
**Key files to prioritize (in order of importance)**:
- **Documentation files**: README*, INSTALL*, BUILD*, BUILDING*, COMPILE*, COMPILATION*, SETUP*, GETTING-STARTED*, NOTES*, GUIDE*, MANUAL*, INSTRUCTION*, HOW-TO*, CONTRIBUTING*
- **Build system files**: Makefile, GNUmakefile, makefile, CMakeLists.txt, configure, configure.in, configure.ac, build.ninja, meson.build, BUILD, BUILD.bazel, xmake.lua, manifest, setup.py, vcpkg.json, build.sh, SConstruct, SConscript
- **Package/dependency files**: package.json, requirements.txt, Cargo.toml, go.mod, pom.xml, build.gradle

**File selection strategy**:
- Always read README-type files first (highest priority for understanding project)
- Read build system files to understand compilation process
- Read documentation that mentions "build", "compile", "install", "dependencies"
- Limit to 3-5 most relevant files to avoid information overload
- Use fuzzy matching for file names (case-insensitive, partial matches)
```

### 2. 代码中实现智能推荐

在`DirectoryAnalyzer`类中添加了`suggest_key_files`方法：

```python
def suggest_key_files(self, directory_structure: str) -> str:
    """
    Suggest key files to read based on directory structure analysis.
    This method provides intelligent file recommendations to assist LLM decision making.
    """
```

### 3. 三层文件优先级策略

**高优先级（High Priority）**：
- 文档文件：README*, INSTALL*, BUILD*, COMPILE*等
- 构建系统文件：Makefile, CMakeLists.txt, configure等

**中优先级（Medium Priority）**：
- 依赖配置文件：package.json, requirements.txt等

**低优先级（Low Priority）**：
- 脚本文件：*.sh, *.py, *.pl, *.cmake等

## 📊 工作流程优化

### 原流程
```
tree . -L 1 → 大模型分析 → 选择文件 → 读取内容
```

### 优化后流程
```
tree . -L 1 → 智能推荐系统 → 大模型决策 → 选择文件 → 读取内容
```

### 新增的FileSuggester工具

在ProjectAnalyzer中新增了FileSuggester工具：
```python
Tool(
    name="FileSuggester",
    description=self.directory_analyzer.suggest_key_files.__doc__,
    func=self.directory_analyzer.suggest_key_files
),
```

## 🎯 关键文件模式匹配

### 文档文件模式
- **关键词匹配**：readme, install, build, building, compile, compilation, setup, getting-started, notes, guide, manual, instruction, how-to, contributing
- **扩展名支持**：.md, .txt, .rst, .markdown, 无扩展名
- **大小写不敏感**：README.md, readme.txt, INSTALL等都能识别

### 构建系统文件
- **Make系列**：Makefile, GNUmakefile, makefile
- **CMake**：CMakeLists.txt
- **Autotools**：configure, configure.in, configure.ac
- **现代构建系统**：build.ninja, meson.build, BUILD.bazel, xmake.lua
- **脚本构建**：build.sh, setup.py

### 依赖管理文件
- **JavaScript/Node.js**：package.json
- **Python**：requirements.txt, setup.py
- **Rust**：Cargo.toml
- **Go**：go.mod
- **Java**：pom.xml, build.gradle
- **C++包管理**：vcpkg.json

## 🚀 实际使用示例

### 1. 典型C++项目分析
```
项目目录：
├── README.md          # 高优先级
├── CMakeLists.txt     # 高优先级
├── INSTALL.txt        # 高优先级
├── src/
├── include/
└── build.sh           # 低优先级

推荐读取：README.md, CMakeLists.txt, INSTALL.txt
```

### 2. 复杂项目分析
```
项目目录：
├── README.md          # 高优先级
├── Makefile           # 高优先级
├── configure.ac       # 高优先级
├── BUILDING.md        # 高优先级
├── requirements.txt   # 中优先级
├── docs/
└── scripts/

推荐读取：README.md, Makefile, configure.ac, BUILDING.md
（限制在3-5个文件内）
```

## 💡 优势总结

### 1. 明确指导
- 提示词中明确列出关键文件类型
- 给出具体的文件选择策略
- 提供优先级排序指导

### 2. 智能辅助
- 代码层面的智能推荐系统
- 自动分类和优先级排序
- 减少大模型的决策负担

### 3. 灵活决策
- 大模型仍有最终决策权
- 可以根据项目特点调整选择
- 支持自定义文件选择

### 4. 效率提升
- 避免读取无关文件
- 限制文件数量（3-5个）
- 优先读取最有价值的文件

## 🎯 与方案文档的一致性

现在实现完全符合方案文档第22行的要求：
> **智能文件识别**：大模型分析目录结构，识别可能包含编译信息的关键文件（如README.md、Makefile、CMakeLists.txt、configure.ac、BUILD等）

通过提示词明确指导 + 代码智能推荐的双重策略，确保了文件识别的准确性和效率。
