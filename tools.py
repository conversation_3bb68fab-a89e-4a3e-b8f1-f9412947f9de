# tools.py - 核心工具类集合（参考AutoCompiler实现）
import os
import json
import uuid
import time
import logging
import subprocess
import tempfile
import re
import chardet
from typing import Dict, List, Optional, Tuple
import paramiko
import requests
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class LineEndingFixer:
    """行尾符修复工具 - 解决Windows/Linux行尾符冲突问题"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def detect_line_endings(self, file_path: str) -> str:
        """
        检测文件的行尾符类型

        @param file_path: 文件路径
        @return: 'CRLF', 'LF', 'CR', 或 'MIXED'
        """
        try:
            with open(file_path, 'rb') as f:
                content = f.read()

            crlf_count = content.count(b'\r\n')
            lf_count = content.count(b'\n') - crlf_count  # 减去CRLF中的LF
            cr_count = content.count(b'\r') - crlf_count  # 减去CRLF中的CR

            if crlf_count > 0 and lf_count == 0 and cr_count == 0:
                return 'CRLF'
            elif lf_count > 0 and crlf_count == 0 and cr_count == 0:
                return 'LF'
            elif cr_count > 0 and crlf_count == 0 and lf_count == 0:
                return 'CR'
            elif crlf_count > 0 or lf_count > 0 or cr_count > 0:
                return 'MIXED'
            else:
                return 'NONE'

        except Exception as e:
            self.logger.error(f"Failed to detect line endings for {file_path}: {str(e)}")
            return 'UNKNOWN'

    def fix_line_endings(self, file_path: str, target_ending: str = 'LF') -> dict:
        """
        修复文件的行尾符

        @param file_path: 文件路径
        @param target_ending: 目标行尾符类型 ('LF', 'CRLF', 'CR')
        @return: 修复结果字典
        """
        try:
            # 检测当前行尾符
            current_ending = self.detect_line_endings(file_path)

            if current_ending == target_ending:
                return {
                    "status": "success",
                    "message": f"File already has {target_ending} line endings",
                    "changed": False,
                    "original_ending": current_ending,
                    "target_ending": target_ending
                }

            # 读取文件内容
            with open(file_path, 'rb') as f:
                content = f.read()

            # 统一转换为LF
            content = content.replace(b'\r\n', b'\n')  # CRLF -> LF
            content = content.replace(b'\r', b'\n')    # CR -> LF

            # 转换为目标格式
            if target_ending == 'CRLF':
                content = content.replace(b'\n', b'\r\n')
            elif target_ending == 'CR':
                content = content.replace(b'\n', b'\r')
            # LF不需要额外处理

            # 写回文件
            with open(file_path, 'wb') as f:
                f.write(content)

            self.logger.info(f"Fixed line endings for {file_path}: {current_ending} -> {target_ending}")

            return {
                "status": "success",
                "message": f"Successfully converted {current_ending} to {target_ending}",
                "changed": True,
                "original_ending": current_ending,
                "target_ending": target_ending
            }

        except Exception as e:
            error_msg = f"Failed to fix line endings for {file_path}: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg,
                "changed": False,
                "error": str(e)
            }

    def fix_directory_line_endings(self, directory_path: str,
                                 file_patterns: list = None,
                                 target_ending: str = 'LF') -> dict:
        """
        批量修复目录中文件的行尾符

        @param directory_path: 目录路径
        @param file_patterns: 文件模式列表，如 ['*.sh', '*.py', '*.c']
        @param target_ending: 目标行尾符类型
        @return: 修复结果统计
        """
        if file_patterns is None:
            # 默认修复常见的脚本和源码文件
            file_patterns = ['*.sh', '*.py', '*.c', '*.cpp', '*.h', '*.hpp',
                           '*.pl', '*.rb', '*.js', '*.php', 'configure',
                           'Makefile*', '*.mk', '*.cmake']

        import glob
        import fnmatch

        results = {
            "total_files": 0,
            "fixed_files": 0,
            "skipped_files": 0,
            "error_files": 0,
            "details": []
        }

        try:
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)

                    # 检查文件是否匹配模式
                    should_fix = False
                    for pattern in file_patterns:
                        if fnmatch.fnmatch(file, pattern) or fnmatch.fnmatch(file_path, pattern):
                            should_fix = True
                            break

                    if not should_fix:
                        continue

                    results["total_files"] += 1
                    fix_result = self.fix_line_endings(file_path, target_ending)

                    if fix_result["status"] == "success":
                        if fix_result["changed"]:
                            results["fixed_files"] += 1
                        else:
                            results["skipped_files"] += 1
                    else:
                        results["error_files"] += 1

                    results["details"].append({
                        "file": file_path,
                        "result": fix_result
                    })

            self.logger.info(f"Line ending fix completed for {directory_path}: "
                           f"{results['fixed_files']} fixed, {results['skipped_files']} skipped, "
                           f"{results['error_files']} errors")

            return results

        except Exception as e:
            error_msg = f"Failed to fix directory line endings: {str(e)}"
            self.logger.error(error_msg)
            return {
                "total_files": 0,
                "fixed_files": 0,
                "skipped_files": 0,
                "error_files": 1,
                "error": error_msg,
                "details": []
            }


class DependencyTracker:
    """循环依赖和递归编译处理器"""

    def __init__(self, max_depth=2):
        self.compilation_path = []      # 当前编译路径 [project_A, dep_B, dep_C]
        self.max_depth = max_depth      # 最大递归深度
        self.compiled_projects = set()  # 已编译项目缓存
    
    def check_dependency_validity(self, new_dependency: str) -> tuple[bool, str]:
        """
        检查新依赖是否可以编译
        返回: (是否可编译, 原因)
        """
        # 1. 检查循环依赖
        if new_dependency in self.compilation_path:
            cycle_path = self.compilation_path + [new_dependency]
            return False, f"Circular dependency detected: {' -> '.join(cycle_path)}"
        
        # 2. 检查递归深度
        if len(self.compilation_path) >= self.max_depth:
            return False, f"Maximum recursion depth ({self.max_depth}) exceeded"
        
        # 3. 检查是否已编译过
        if new_dependency in self.compiled_projects:
            return True, f"Dependency {new_dependency} already compiled, skipping"
        
        return True, "Dependency is valid for compilation"
    
    def enter_dependency(self, dependency: str):
        """进入依赖编译"""
        self.compilation_path.append(dependency)
        logging.info(f"Entering dependency compilation: {' -> '.join(self.compilation_path)}")
    
    def exit_dependency(self, dependency: str, success: bool):
        """退出依赖编译"""
        if self.compilation_path and self.compilation_path[-1] == dependency:
            self.compilation_path.pop()
            
        if success:
            self.compiled_projects.add(dependency)
            logging.info(f"Successfully compiled dependency: {dependency}")
        else:
            logging.warning(f"Failed to compile dependency: {dependency}")
    
    def get_current_depth(self) -> int:
        """获取当前递归深度"""
        return len(self.compilation_path)
    
    def get_compilation_path(self) -> list:
        """获取当前编译路径"""
        return self.compilation_path.copy()


class DependencyScanner:
    """依赖扫描工具类 - 封装ccscanner"""
    
    def __init__(self, confidence_threshold=['High', 'Medium']):
        """
        Initialize dependency scanner with confidence filtering.

        @param confidence_threshold: List of acceptable confidence levels
        """
        self.confidence_threshold = confidence_threshold
        self.logger = []

    def scan_dependencies(self, project_path: str) -> str:
        """
        Execute ccscanner tool for structured dependency scanning of C/C++ projects.
        Filters results by confidence level (High/Medium only) and processes version conflicts.

        @param project_path: Absolute path to the project directory
        @return: JSON string containing filtered and processed dependency information
        """
        try:
            # 调用ccscanner并处理结果
            scan_results = self._run_ccscanner(project_path)
            processed_deps = self._process_ccscanner_results(scan_results)

            self.logger.append([
                "scan_dependencies",
                project_path,
                f"Found {len(processed_deps)} dependencies"
            ])

            return json.dumps({
                "dependencies": processed_deps,
                "scan_status": "success",
                "total_count": len(processed_deps)
            })
        except Exception as e:
            return json.dumps({
                "dependencies": [],
                "scan_status": "failed",
                "error": str(e)
            })

    def _run_ccscanner(self, project_path: str) -> dict:
        """Execute ccscanner command and return raw results"""
        try:
            # 创建临时文件保存ccscanner结果
            with tempfile.NamedTemporaryFile(mode='w+', suffix='.json', delete=False) as temp_file:
                temp_path = temp_file.name

            # 执行ccscanner命令
            cmd = f"ccscanner_print -d {project_path} -t {temp_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)

            if result.returncode != 0:
                self.logger.append(["_run_ccscanner", project_path, f"ccscanner failed: {result.stderr}"])
                return {}

            # 读取ccscanner结果
            try:
                with open(temp_path, 'r', encoding='utf-8') as f:
                    scan_results = json.load(f)
                os.unlink(temp_path)  # 清理临时文件
                return scan_results
            except (json.JSONDecodeError, FileNotFoundError) as e:
                self.logger.append(["_run_ccscanner", project_path, f"Failed to parse ccscanner output: {str(e)}"])
                return {}

        except Exception as e:
            self.logger.append(["_run_ccscanner", project_path, f"ccscanner execution error: {str(e)}"])
            return {}

    def _process_ccscanner_results(self, scan_results: dict) -> list:
        """
        Process ccscanner raw results: filter by confidence, handle version conflicts.
        Takes union of all extractor results and resolves duplicate dependencies.
        """
        if not scan_results:
            return []
        
        processed_deps = []
        seen_deps = set()
        
        # 处理所有提取器的结果
        for extractor_name, extractor_data in scan_results.items():
            if not isinstance(extractor_data, dict) or 'dependencies' not in extractor_data:
                continue
                
            for dep in extractor_data['dependencies']:
                if not isinstance(dep, dict):
                    continue
                    
                # 置信度筛选
                confidence = dep.get('confidence', 'Low')
                if confidence not in self.confidence_threshold:
                    continue
                
                dep_name = dep.get('name', '').strip()
                if not dep_name or dep_name in seen_deps:
                    continue
                
                # 标准化依赖信息
                processed_dep = {
                    "name": dep_name,
                    "install_method": self._determine_install_method(dep_name),
                    "version": dep.get('version', 'latest'),
                    "source_url": dep.get('source_url'),
                    "confidence": self._normalize_confidence(confidence),
                    "extractor": extractor_name
                }
                
                processed_deps.append(processed_dep)
                seen_deps.add(dep_name)
        
        return processed_deps
    
    def _determine_install_method(self, dep_name: str) -> str:
        """根据依赖名称判断安装方式"""
        # 系统级包管理器优先
        system_packages = ['gcc', 'g++', 'make', 'cmake', 'autotools', 'pkg-config']
        if any(pkg in dep_name.lower() for pkg in system_packages):
            return 'apt'
        
        # 开发库
        if dep_name.endswith('-dev') or 'lib' in dep_name.lower():
            return 'apt'
        
        # 默认尝试apt，失败时可能需要源码编译
        return 'apt'
    
    def _normalize_confidence(self, confidence: str) -> float:
        """将置信度字符串转换为数值"""
        confidence_map = {
            'High': 0.9,
            'Medium': 0.7,
            'Low': 0.3
        }
        return confidence_map.get(confidence, 0.5)


class InteractiveDockerShell:
    """Docker交互式Shell管理器 - 完全参考AutoCompiler实现"""

    HOSTNAME = 'c0mpi1er-c0nta1ner'  # 使用AutoCompiler的主机名

    # 多版本Docker镜像支持
    AVAILABLE_IMAGES = {
        "18.04": "ubuntu:18.04",
        "20.04": "ubuntu:20.04",
        "22.04": "ubuntu:22.04"
    }
    DEFAULT_VERSION = "20.04"

    def __init__(self, local_path, ubuntu_version=None, use_proxy=False,
                 stuck_timeout=120, cmd_timeout=3600, pre_exec=True):
        """
        简化版本 - 直接使用docker exec，不依赖SSH
        """
        # 确定使用的Ubuntu版本和镜像
        self.ubuntu_version = ubuntu_version or self.DEFAULT_VERSION
        if self.ubuntu_version not in self.AVAILABLE_IMAGES:
            raise Exception(f"Unsupported Ubuntu version: {self.ubuntu_version}")

        image_name = self.AVAILABLE_IMAGES[self.ubuntu_version]
        self.local_path = local_path

        try:
            # 创建容器 - 添加调试信息
            logging.info(f"Creating container with image: {image_name}")
            logging.info(f"Local path: {local_path}")

            # 确保使用绝对路径进行Docker挂载，避免路径中的换行符问题
            abs_local_path = os.path.abspath(local_path).replace('\n', '').replace('\r', '')
            container_cmd = f"docker run --privileged --network bridge --hostname {self.HOSTNAME} -e DEBIAN_FRONTEND=noninteractive -e TZ=UTC -v \"{abs_local_path}\":/work -itd {image_name} /bin/bash"
            logging.info(f"Docker command: {container_cmd}")

            result = subprocess.run(container_cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                raise Exception(f"Failed to create container: {result.stderr}\nCommand: {container_cmd}")

            container_id = result.stdout.strip()
            if len(container_id) != 64:
                raise Exception(f"Invalid container ID: {container_id}")

            logging.info(f"[+] Container {container_id} created.")

            # 安装基本工具
            setup_commands = [
                "apt-get update",
                "apt-get install -y build-essential gcc g++ make cmake autoconf automake libtool pkg-config git"
            ]

            for cmd in setup_commands:
                result = subprocess.run(f"docker exec {container_id} /bin/bash -c '{cmd}'",
                                      shell=True, capture_output=True, text=True)
                if result.returncode != 0:
                    logging.warning(f"Setup command failed: {cmd}")

        except Exception as e:
            raise Exception(f"Failed to create the container, error: {str(e)}")

        # 不使用SSH，直接使用docker exec
        self.client = None
        self.session = None
        self.stuck_timeout = stuck_timeout
        self.cmd_timeout = cmd_timeout
        self.container_id = container_id
        self.last_line = "root@c0mpi1er-c0nta1ner:/work# "
        self.logger = []
        self.use_proxy = use_proxy

        if pre_exec:
            # 初始化容器环境
            self.execute_command("cd /work && pwd")
            # 更新包管理器并修复权限问题
            self.execute_command("apt-get update || (mkdir -p /var/lib/apt/lists/partial && chmod 755 /var/lib/apt/lists/partial && apt-get update)")

    def execute_command(self, command: str) -> str:
        """
        Execute a command using docker exec (simplified version without SSH).
        @param command: The command to execute.
        """
        if not self.container_id:
            raise Exception("No container available.")

        # 命令预处理
        command = command.strip()
        for wrap in ["`", "\"", "**", "```"]:
            if command.startswith(wrap) and command.endswith(wrap):
                command = command[len(wrap):-len(wrap)]

        # 代理处理（如果启用）
        if self.use_proxy:
            if "git " in command and "proxychains git " not in command:
                command = command.replace("git ", "proxychains -q git ")
            if "curl " in command and "proxychains curl " not in command:
                command = command.replace("curl ", "proxychains -q curl ")
            if "wget " in command and "proxychains wget " not in command:
                command = command.replace("wget ", "proxychains -q wget ")

        # 自动添加-y参数
        if "apt install " in command:
            command = command.replace("apt install ", "apt install -y ")
        if "apt-get install " in command:
            command = command.replace("apt-get install ", "apt-get install -y ")

        # 特殊命令处理
        if "make install" in command:
            return "Tips: Do not install the project, just compile it!"
        if "make" == command.strip():
            command = "make -j$(nproc)"  # 自动并行编译

        # 执行命令
        cmd_start_time = time.time()

        try:
            # 使用docker exec执行命令 - 改进的命令处理
            # 对于包含特殊字符的命令，使用更安全的方式
            if any(char in command for char in ['"', "'", '|', '>', '<', '&', ';', '$(', '`']):
                # 复杂命令：写入临时脚本文件执行
                result = self._execute_complex_command(command)
            else:
                # 简单命令：直接执行
                result = self._execute_simple_command(command)

            duration = time.time() - cmd_start_time

            # 组合输出
            output = ""
            if result.stdout:
                output += result.stdout
            if result.stderr:
                output += "\n" + result.stderr

            # 添加命令提示符
            output += f"\nroot@{self.HOSTNAME}:/work# "

            return self.omit(command, output, duration)

        except subprocess.TimeoutExpired:
            duration = time.time() - cmd_start_time
            output = f"Command execution timeout after {self.cmd_timeout} seconds!\nroot@{self.HOSTNAME}:/work# "
            return self.omit(command, output, duration)
        except Exception as e:
            duration = time.time() - cmd_start_time
            output = f"Command execution failed: {str(e)}\nroot@{self.HOSTNAME}:/work# "
            return self.omit(command, output, duration)

    def _execute_complex_command(self, command: str):
        """
        执行复杂命令（包含特殊字符、重定向等）
        使用临时脚本文件方式，确保命令正确执行
        """
        # 创建脚本内容，确保使用Unix行尾符
        script_content = f"#!/bin/bash\nset -e\ncd /work\n{command}\n"

        # 创建临时脚本文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False, newline='\n') as temp_script:
            temp_script.write(script_content)
            temp_script_path = temp_script.name

        try:
            # 使用LineEndingFixer确保脚本文件使用正确的行尾符
            line_fixer = LineEndingFixer()
            line_fixer.fix_line_endings(temp_script_path, 'LF')

            # 复制脚本到容器
            copy_cmd = f"docker cp {temp_script_path} {self.container_id}:/tmp/exec_script.sh"
            copy_result = subprocess.run(copy_cmd, shell=True, capture_output=True, text=True)

            if copy_result.returncode != 0:
                raise Exception(f"Failed to copy script to container: {copy_result.stderr}")

            # 设置脚本执行权限并执行
            chmod_cmd = f"docker exec {self.container_id} chmod +x /tmp/exec_script.sh"
            subprocess.run(chmod_cmd, shell=True, capture_output=True)

            # 执行脚本
            docker_cmd = f"docker exec {self.container_id} /bin/bash /tmp/exec_script.sh"
            result = subprocess.run(
                docker_cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=self.cmd_timeout
            )

            # 清理容器中的临时脚本
            cleanup_cmd = f"docker exec {self.container_id} rm -f /tmp/exec_script.sh"
            subprocess.run(cleanup_cmd, shell=True, capture_output=True)

            return result

        finally:
            # 清理本地临时文件
            if os.path.exists(temp_script_path):
                os.unlink(temp_script_path)

    def _execute_simple_command(self, command: str):
        """
        执行简单命令（不包含特殊字符）
        直接使用docker exec执行
        """
        # 对命令进行适当的转义
        escaped_command = command.replace("'", "'\"'\"'")  # 转义单引号
        docker_cmd = f"docker exec -w /work {self.container_id} /bin/bash -c '{escaped_command}'"

        result = subprocess.run(
            docker_cmd,
            shell=True,
            capture_output=True,
            text=True,
            timeout=self.cmd_timeout
        )

        return result

    def omit(self, command, output, duration) -> str:
        """
        完全参考AutoCompiler的智能输出省略策略
        """
        output = re.sub(r'\x1B[@-_][0-?]*[ -/]*[@-~]', '', output)  # 移除ANSI转义字符
        output = self.last_line + output
        self.logger.append([command, output, duration])

        # 获取输出的最后一行
        self.last_line = output.split("\n")[-1]

        # 智能输出省略
        if command.startswith("make"):
            return "\n".join(output.split("\n")[-50:])  # make命令只保留最后50行
        elif command.startswith("configure") or command.startswith("./configure"):
            return "\n".join(output.split("\n")[-30:])  # configure命令只保留最后30行
        elif command.startswith("cmake"):
            return "\n".join(output.split("\n")[-30:])  # cmake命令只保留最后30行
        else:
            if len(output) > 8000:
                output = output[:4000] + "\n......\n" + output[-4000:]  # 中间省略策略
            return output

    def create_git_snapshot(self, project_path: str) -> str:
        """
        Create git snapshot for compilation artifact detection.

        @param project_path: Path to project directory in container
        @return: Status message
        """
        try:
            # 初始化git仓库（如果不存在）
            init_result = self.execute_command(f"cd {project_path} && git init")

            # 添加所有文件到git
            add_result = self.execute_command(f"cd {project_path} && git add .")

            # 创建初始提交
            commit_result = self.execute_command(f"cd {project_path} && git commit -m 'Initial snapshot before compilation'")

            return "Git snapshot created successfully"
        except Exception as e:
            return f"Failed to create git snapshot: {str(e)}"

    def detect_compilation_artifacts(self, project_path: str) -> str:
        """
        Detect compilation artifacts using multiple strategies.

        @param project_path: Path to project directory in container
        @return: JSON string with detected artifacts
        """
        try:
            artifacts = []

            # 策略1: 使用git检测新文件（如果有git仓库）
            git_artifacts = self._detect_artifacts_by_git(project_path)
            artifacts.extend(git_artifacts)

            # 策略2: 使用find命令查找常见的编译产物
            find_artifacts = self._detect_artifacts_by_find(project_path)
            artifacts.extend(find_artifacts)

            # 策略3: 检查常见的构建目录
            build_artifacts = self._detect_artifacts_in_build_dirs(project_path)
            artifacts.extend(build_artifacts)

            # 去重和合并结果
            unique_artifacts = self._deduplicate_artifacts(artifacts)

            # 增强文件信息
            enhanced_artifacts = []
            for artifact in unique_artifacts:
                enhanced = self._enhance_artifact_info(project_path, artifact)
                if enhanced:
                    enhanced_artifacts.append(enhanced)

            return json.dumps({
                "artifacts": enhanced_artifacts,
                "status": "success",
                "total_count": len(enhanced_artifacts),
                "detection_methods": ["git", "find", "build_dirs"]
            })

        except Exception as e:
            return json.dumps({
                "artifacts": [],
                "status": "failed",
                "error": str(e)
            })

    def _detect_artifacts_by_git(self, project_path: str) -> list:
        """使用git检测新文件"""
        artifacts = []
        try:
            # 检测未跟踪的文件
            result = self.execute_command(f"cd {project_path} && git ls-files --others --exclude-standard 2>/dev/null")

            if "fatal:" not in result and "not a git repository" not in result:
                lines = [line.strip() for line in result.strip().split('\n') if line.strip()]
                for line in lines:
                    if self._is_likely_artifact(line):
                        artifacts.append({"path": line, "detection_method": "git"})
        except:
            pass
        return artifacts

    def _detect_artifacts_by_find(self, project_path: str) -> list:
        """使用find命令查找编译产物"""
        artifacts = []
        try:
            # 查找可执行文件和库文件
            find_cmd = (
                f"cd {project_path} && find . -type f "
                f"\\( -name '*.so' -o -name '*.so.*' -o -name '*.a' -o -name '*.dylib' "
                f"-o -name '*.dll' -o -name '*.exe' -o -perm -111 \\) "
                f"-not -path './.git/*' -not -path './.*' 2>/dev/null"
            )
            result = self.execute_command(find_cmd)

            lines = [line.strip() for line in result.strip().split('\n') if line.strip()]
            for line in lines:
                # 移除 ./ 前缀
                clean_path = line.lstrip('./')
                if clean_path and self._is_likely_artifact(clean_path):
                    artifacts.append({"path": clean_path, "detection_method": "find"})
        except:
            pass
        return artifacts

    def _detect_artifacts_in_build_dirs(self, project_path: str) -> list:
        """检查常见构建目录中的产物"""
        artifacts = []
        build_dirs = ['build', 'bin', 'lib', 'out', 'target', 'dist', '.libs']

        for build_dir in build_dirs:
            try:
                find_cmd = (
                    f"cd {project_path} && find {build_dir} -type f "
                    f"\\( -name '*.so' -o -name '*.so.*' -o -name '*.a' -o -name '*.dylib' "
                    f"-o -name '*.dll' -o -name '*.exe' -o -perm -111 \\) 2>/dev/null"
                )
                result = self.execute_command(find_cmd)

                lines = [line.strip() for line in result.strip().split('\n') if line.strip()]
                for line in lines:
                    if line and self._is_likely_artifact(line):
                        artifacts.append({"path": line, "detection_method": "build_dir"})
            except:
                continue

        return artifacts

    def _is_likely_artifact(self, filepath: str) -> bool:
        """判断文件是否可能是编译产物"""
        # 排除源码文件和其他非编译产物
        exclude_extensions = ['.c', '.cpp', '.cc', '.cxx', '.h', '.hpp', '.hxx',
                            '.py', '.java', '.js', '.txt', '.md', '.rst', '.xml',
                            '.json', '.yaml', '.yml', '.ini', '.cfg', '.conf']

        # 排除特定文件
        exclude_files = ['Makefile', 'CMakeLists.txt', 'configure', 'config.log',
                        'config.status', 'libtool', 'compile', 'install-sh']

        filename = os.path.basename(filepath)

        # 检查是否在排除列表中
        if filename in exclude_files:
            return False

        # 检查扩展名
        for ext in exclude_extensions:
            if filepath.endswith(ext):
                return False

        # 包含的编译产物类型
        include_extensions = ['.so', '.a', '.dylib', '.dll', '.exe', '.out']
        for ext in include_extensions:
            if filepath.endswith(ext) or ext in filepath:  # 处理 .so.1.2.3 这样的情况
                return True

        # 检查是否在构建目录中
        build_paths = ['/build/', '/bin/', '/lib/', '/out/', '/target/', '/dist/', '/.libs/']
        for build_path in build_paths:
            if build_path in filepath:
                return True

        return False

    def _deduplicate_artifacts(self, artifacts: list) -> list:
        """去重编译产物列表"""
        seen_paths = set()
        unique_artifacts = []

        for artifact in artifacts:
            path = artifact["path"]
            if path not in seen_paths:
                seen_paths.add(path)
                unique_artifacts.append(artifact)

        return unique_artifacts

    def _enhance_artifact_info(self, project_path: str, artifact: dict) -> dict:
        """增强编译产物信息"""
        try:
            filepath = artifact["path"]

            # 检查文件是否存在
            check_cmd = f"cd {project_path} && test -f '{filepath}' && echo 'exists' || echo 'not_exists'"
            check_result = self.execute_command(check_cmd)

            if 'not_exists' in check_result:
                return None

            # 获取文件信息
            stat_cmd = f"cd {project_path} && stat -c '%s %Y %A' '{filepath}' 2>/dev/null"
            stat_result = self.execute_command(stat_cmd)

            try:
                stat_parts = stat_result.strip().split()
                file_size = int(stat_parts[0])
                mtime = int(stat_parts[1])
                permissions = stat_parts[2]
                is_executable = 'x' in permissions
            except:
                file_size = 0
                mtime = 0
                permissions = ''
                is_executable = False

            # 获取文件类型
            file_cmd = f"cd {project_path} && file '{filepath}' 2>/dev/null"
            file_result = self.execute_command(file_cmd)
            file_type_info = file_result.strip().split(':', 1)[-1].strip() if ':' in file_result else ''

            # 分类编译产物
            artifact_type = self._classify_artifact(filepath, is_executable, file_type_info)

            return {
                "path": filepath,
                "type": artifact_type,
                "size": file_size,
                "executable": is_executable,
                "permissions": permissions,
                "mtime": mtime,
                "file_type": file_type_info,
                "detection_method": artifact.get("detection_method", "unknown")
            }

        except Exception as e:
            return {
                "path": artifact["path"],
                "type": "unknown",
                "size": 0,
                "executable": False,
                "error": str(e),
                "detection_method": artifact.get("detection_method", "unknown")
            }

    def _classify_artifact(self, filename: str, is_executable: bool, file_type_info: str = '') -> str:
        """分类编译产物"""
        filename_lower = filename.lower()

        # 基于文件扩展名的分类
        if filename_lower.endswith('.so') or '.so.' in filename_lower:
            return 'dynamic_library'
        elif filename_lower.endswith('.a'):
            return 'static_library'
        elif filename_lower.endswith('.o'):
            return 'object_file'
        elif filename_lower.endswith('.dylib'):
            return 'dynamic_library'
        elif filename_lower.endswith('.dll'):
            return 'dynamic_library'
        elif filename_lower.endswith('.exe'):
            return 'executable'
        elif filename_lower.endswith('.out'):
            return 'executable'

        # 基于file命令输出的分类
        if file_type_info:
            file_type_lower = file_type_info.lower()
            if 'shared object' in file_type_lower or 'shared library' in file_type_lower:
                return 'dynamic_library'
            elif 'archive' in file_type_lower and 'ar archive' in file_type_lower:
                return 'static_library'
            elif 'executable' in file_type_lower:
                return 'executable'
            elif 'relocatable' in file_type_lower:
                return 'object_file'

        # 基于可执行权限的分类
        if is_executable:
            # 可执行文件通常没有扩展名或有特定扩展名
            if '.' not in os.path.basename(filename) or filename_lower.endswith('.bin'):
                return 'executable'

        return 'unknown'

    def collect_artifacts_to_host(self, project_path: str, host_output_dir: str) -> dict:
        """
        将编译产物从容器复制到主机

        @param project_path: 容器中的项目路径
        @param host_output_dir: 主机上的输出目录
        @return: 收集结果
        """
        try:
            # 确保主机输出目录存在
            os.makedirs(host_output_dir, exist_ok=True)

            # 检测编译产物
            artifacts_json = self.detect_compilation_artifacts(project_path)
            artifacts_data = json.loads(artifacts_json)

            if artifacts_data["status"] != "success":
                return {
                    "status": "failed",
                    "error": "Failed to detect artifacts",
                    "details": artifacts_data
                }

            artifacts = artifacts_data["artifacts"]
            collected_artifacts = []
            failed_artifacts = []

            for artifact in artifacts:
                try:
                    source_path = f"{self.container_id}:{project_path}/{artifact['path']}"
                    target_filename = os.path.basename(artifact['path'])
                    target_path = os.path.join(host_output_dir, target_filename)

                    # 如果目标文件已存在，添加后缀避免冲突
                    counter = 1
                    original_target = target_path
                    while os.path.exists(target_path):
                        name, ext = os.path.splitext(original_target)
                        target_path = f"{name}_{counter}{ext}"
                        counter += 1

                    # 使用docker cp复制文件
                    copy_cmd = f"docker cp {source_path} {target_path}"
                    copy_result = subprocess.run(copy_cmd, shell=True, capture_output=True, text=True)

                    if copy_result.returncode == 0:
                        # 验证文件是否成功复制
                        if os.path.exists(target_path):
                            file_size = os.path.getsize(target_path)
                            collected_artifacts.append({
                                "source_path": artifact['path'],
                                "target_path": target_path,
                                "type": artifact['type'],
                                "size": file_size,
                                "executable": artifact.get('executable', False)
                            })
                        else:
                            failed_artifacts.append({
                                "path": artifact['path'],
                                "error": "File not found after copy"
                            })
                    else:
                        failed_artifacts.append({
                            "path": artifact['path'],
                            "error": copy_result.stderr
                        })

                except Exception as e:
                    failed_artifacts.append({
                        "path": artifact['path'],
                        "error": str(e)
                    })

            # 生成元数据文件
            metadata = {
                "project_path": project_path,
                "collection_time": time.time(),
                "total_artifacts": len(artifacts),
                "collected_count": len(collected_artifacts),
                "failed_count": len(failed_artifacts),
                "collected_artifacts": collected_artifacts,
                "failed_artifacts": failed_artifacts
            }

            metadata_file = os.path.join(host_output_dir, "artifacts_metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

            return {
                "status": "success",
                "collected_count": len(collected_artifacts),
                "failed_count": len(failed_artifacts),
                "output_dir": host_output_dir,
                "metadata_file": metadata_file,
                "collected_artifacts": collected_artifacts
            }

        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "collected_count": 0,
                "failed_count": 0
            }

    def switch_ubuntu_version(self, target_version: str) -> str:
        """
        Switch to different Ubuntu version container for system compatibility issues.
        由主控智能体调用的版本切换策略

        @param target_version: Target Ubuntu version (18.04/20.04/22.04)
        @return: Switch result message
        """
        if target_version not in self.AVAILABLE_IMAGES:
            return f"Error: Unsupported Ubuntu version: {target_version}"

        if target_version == self.ubuntu_version:
            return f"Already using Ubuntu {target_version}"

        try:
            # 销毁当前容器
            logging.info(f"[-] Switching from Ubuntu {self.ubuntu_version} to {target_version}")
            self.close()

            # 重新初始化为新版本容器
            self.__init__(
                local_path=self.local_path,
                ubuntu_version=target_version,
                use_proxy=False,
                stuck_timeout=self.stuck_timeout,
                cmd_timeout=self.cmd_timeout,
                pre_exec=True
            )

            logging.info(f"[+] Successfully switched to Ubuntu {target_version}")
            return f"Successfully switched to Ubuntu {target_version}"

        except Exception as e:
            return f"Failed to switch to Ubuntu {target_version}: {str(e)}"

    def close(self):
        """清理Docker容器资源"""
        logging.info("[-] Stopping docker container, please wait a second...")
        if self.client:
            self.client.close()
        if self.container_id:
            subprocess.run(f"docker stop {self.container_id}", shell=True, capture_output=True)
            subprocess.run(f"docker rm {self.container_id}", shell=True, capture_output=True)
            logging.info(f"[-] Container {self.container_id[:12]} stopped and removed")

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class GitHubManager:
    """GitHub项目管理工具类"""

    def __init__(self, proxy=None, github_token=None):
        self.proxy = proxy
        self.github_token = github_token
        self.logger = []

    def clone_project(self, url: str, local_path: str) -> str:
        """
        Download project from GitHub repository. 完全采用AutoCompiler的实现。

        @param url: GitHub repository URL
        @param local_path: Local path to clone the project
        @return: Status message
        """
        try:
            logging.info(f"[-] Downloading project from {url} to {local_path}")

            if self.proxy is not None:
                cmd = f"git clone {url} {local_path} --config http.proxy={self.proxy}"
            else:
                cmd = f"git clone {url} {local_path}"

            logging.info(f"[-] Running command: {cmd}")
            ret = subprocess.run(cmd, shell=True, capture_output=True)

            if ret.returncode != 0:
                try:
                    error_msg = f"Failed to download project from {url}\n\n{ret.stderr.decode('utf-8')}"
                except UnicodeDecodeError:
                    error_msg = f"Failed to download project from {url}\n\n{ret.stderr.decode('utf-8', errors='ignore')}"
                self.logger.append(["clone_project", url, error_msg])
                return f"Error: {error_msg}"

            # 修复行尾符问题 - 在下载后立即处理
            try:
                self._fix_line_endings(local_path)
                logging.info(f"[-] Fixed line endings for project at {local_path}")
            except Exception as e:
                logging.warning(f"[-] Failed to fix line endings: {str(e)}")

            self.logger.append(["clone_project", url, "Success"])
            return f"Successfully cloned project to {local_path}"

        except Exception as e:
            error_msg = f"Exception during clone: {str(e)}"
            self.logger.append(["clone_project", url, error_msg])
            return f"Error: {error_msg}"

    def create_project_copy(self, local_path: str) -> str:
        """
        Create project copy with UUID. 参考AutoCompiler但简化为单次调用。
        下载项目后调用一次即可，无需复杂的副本管理。

        @param local_path: Original project path
        @return: New copy path or error message
        """
        try:
            UUID = uuid.uuid4()
            local_path = os.path.abspath(local_path)
            new_path = f"{local_path}-{UUID}"

            # Windows兼容的复制命令
            import sys
            if sys.platform == "win32":
                cmd = f'xcopy "{local_path}" "{new_path}" /E /I /H /Y'
            else:
                cmd = f"chmod -R 777 {local_path} && cp -r {local_path} {new_path} && chmod -R 777 {new_path}"
            logging.info(f"[-] Copy project from {local_path} to {new_path}")

            ret = subprocess.run(cmd, shell=True, capture_output=True)
            if ret.returncode != 0:
                try:
                    error_msg = f"Failed to copy project: {ret.stderr.decode('utf-8')}"
                except UnicodeDecodeError:
                    error_msg = f"Failed to copy project: {ret.stderr.decode('utf-8', errors='ignore')}"
                self.logger.append(["create_project_copy", local_path, error_msg])
                return f"Error: {error_msg}"

            self.logger.append(["create_project_copy", local_path, f"Success: {new_path}"])
            return new_path

        except Exception as e:
            error_msg = f"Exception during copy: {str(e)}"
            self.logger.append(["create_project_copy", local_path, error_msg])
            return f"Error: {error_msg}"

    def _fix_line_endings(self, project_path: str):
        """修复项目中shell脚本的行尾符问题"""
        import os

        # 需要修复的文件类型
        script_files = []

        # 查找shell脚本文件
        for root, dirs, files in os.walk(project_path):
            for file in files:
                file_path = os.path.join(root, file)
                # 检查常见的脚本文件
                if (file in ['configure', 'autogen.sh', 'bootstrap'] or
                    file.endswith('.sh') or
                    file.endswith('.pl') or
                    file.endswith('.py')):
                    script_files.append(file_path)

        # 修复每个脚本文件
        for script_file in script_files:
            try:
                # 读取文件内容
                with open(script_file, 'rb') as f:
                    content = f.read()

                # 检查是否包含Windows行尾符
                if b'\r\n' in content:
                    # 转换为Unix行尾符
                    fixed_content = content.replace(b'\r\n', b'\n')

                    # 写回文件
                    with open(script_file, 'wb') as f:
                        f.write(fixed_content)

                    logging.info(f"[-] Fixed line endings in {script_file}")

            except Exception as e:
                logging.warning(f"[-] Failed to fix line endings in {script_file}: {str(e)}")
                continue

    def search_issues(self, project_url: str, error_keywords: str, max_results: int = 10) -> str:
        """
        Search GitHub Issues for project-specific solutions.

        @param project_url: GitHub project URL
        @param error_keywords: Keywords extracted from error message
        @param max_results: Maximum number of results to return
        @return: JSON string with search results
        """
        try:
            # 从URL提取owner和repo
            if 'github.com' not in project_url:
                return json.dumps({"issues": [], "status": "invalid_url"})

            parts = project_url.replace('https://github.com/', '').replace('.git', '').split('/')
            if len(parts) < 2:
                return json.dumps({"issues": [], "status": "invalid_url"})

            owner, repo = parts[0], parts[1]

            # 构建GitHub API搜索URL
            search_query = f"repo:{owner}/{repo} {error_keywords}"
            api_url = f"https://api.github.com/search/issues?q={search_query}&per_page={max_results}"

            headers = {}
            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'

            response = requests.get(api_url, headers=headers, timeout=30)

            if response.status_code != 200:
                return json.dumps({
                    "issues": [],
                    "status": "api_error",
                    "error": f"HTTP {response.status_code}"
                })

            data = response.json()
            issues = []

            for item in data.get('items', []):
                issues.append({
                    "title": item.get('title', ''),
                    "body": item.get('body', '')[:500],  # 限制长度
                    "url": item.get('html_url', ''),
                    "state": item.get('state', ''),
                    "comments": item.get('comments', 0)
                })

            return json.dumps({
                "issues": issues,
                "status": "success",
                "total_count": len(issues)
            })

        except Exception as e:
            return json.dumps({
                "issues": [],
                "status": "failed",
                "error": str(e)
            })


class DirectoryAnalyzer:
    """目录结构分析工具类 - 简化的智能文件选择方案"""

    def __init__(self, project_path: str):
        """
        Initialize directory analyzer for intelligent file selection.

        @param project_path: Path to the project directory
        """
        self.project_path = project_path
        self.logger = []

    def get_directory_structure(self, depth: int = 1) -> str:
        """
        Get project directory structure using tree command.

        @param depth: Directory depth to analyze (default: 1 for root level)
        @return: JSON string containing directory structure
        """
        try:
            # 执行tree命令获取目录结构
            cmd = f"tree {self.project_path} -L {depth} -a -I '.git|__pycache__|*.pyc|node_modules'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                tree_output = result.stdout
            else:
                # 如果tree命令不可用，使用ls作为备选
                cmd = f"ls -la {self.project_path}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
                tree_output = result.stdout if result.returncode == 0 else "Directory listing failed"

            return json.dumps({
                "directory_structure": tree_output,
                "project_path": self.project_path,
                "status": "success"
            })

        except Exception as e:
            return json.dumps({
                "directory_structure": "",
                "project_path": self.project_path,
                "status": "failed",
                "error": str(e)
            })

    def read_selected_files(self, file_list: str) -> str:
        """
        Read content of selected files based on LLM analysis.

        @param file_list: JSON string containing list of files to read
        @return: JSON string containing file contents
        """
        try:
            files_to_read = json.loads(file_list) if isinstance(file_list, str) else file_list
            file_contents = {}

            for filename in files_to_read:
                file_path = os.path.join(self.project_path, filename)

                if os.path.exists(file_path) and os.path.isfile(file_path):
                    try:
                        # 限制文件大小，避免读取过大文件
                        if os.path.getsize(file_path) > 100 * 1024:  # 100KB限制
                            file_contents[filename] = "File too large, skipped"
                            continue

                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            # 限制内容长度
                            if len(content) > 10000:
                                content = content[:5000] + "\n...[truncated]...\n" + content[-5000:]
                            file_contents[filename] = content

                    except Exception as e:
                        file_contents[filename] = f"Error reading file: {str(e)}"
                else:
                    file_contents[filename] = "File not found"

            return json.dumps({
                "file_contents": file_contents,
                "files_read": len(file_contents),
                "status": "success"
            })

        except Exception as e:
            return json.dumps({
                "file_contents": {},
                "files_read": 0,
                "status": "failed",
                "error": str(e)
            })


class DocumentAnalyzer:
    """智能文档解析工具类 - 保留原有实现作为备用"""

    def __init__(self, project_path: str, project_name: str):
        """
        Initialize document analyzer with RAG configuration.

        @param project_path: Path to the project directory
        @param project_name: Name of the project being analyzed
        """
        self.project_path = project_path
        self.project_name = project_name
        self.logger = []

        # 文档发现策略（改进的模糊匹配，参考AutoCompiler但更全面）
        self.compilation_keywords = [
            "readme", "build", "install", "contributing", "how-to", "compile",
            "compilation", "make", "cmake", "configure", "setup", "getting-started",
            "notes", "doc", "guide", "manual", "instruction", "building"
        ]
        self.document_extensions = ['.md', '.txt', '.rst', '.markdown']

        # 支持的构建系统
        self.build_system_files = {
            'CMake': ['CMakeLists.txt'],
            'Make': ['Makefile', 'GNUmakefile', 'makefile'],
            'Autotools': ['configure', 'configure.in', 'configure.ac'],
            'Ninja': ['build.ninja'],
            'Meson': ['meson.build']
        }

    def analyze_documents(self, project_path: str = None) -> str:
        """
        Analyze project documentation files using direct LLM analysis.
        Discovers and analyzes README, BUILD, INSTALL and other relevant files.

        @param project_path: Path to project directory (optional, uses self.project_path if not provided)
        @return: JSON string containing extracted dependencies and build instructions
        """
        target_path = project_path or self.project_path

        try:
            # 发现相关文档文件
            doc_files = self._discover_documentation_files(target_path)

            if not doc_files:
                return json.dumps({
                    "dependencies": [],
                    "build_commands": [],
                    "build_system": "unknown",
                    "status": "no_docs_found"
                })

            # 分析每个文档文件
            all_dependencies = []
            all_build_commands = []
            build_system = "unknown"

            for doc_file in doc_files:
                analysis = self._analyze_single_document(doc_file)
                if analysis:
                    all_dependencies.extend(analysis.get('dependencies', []))
                    all_build_commands.extend(analysis.get('build_commands', []))
                    if analysis.get('build_system') != 'unknown':
                        build_system = analysis.get('build_system')

            # 去重和整理
            unique_deps = self._deduplicate_dependencies(all_dependencies)
            clean_commands = self._clean_build_commands(all_build_commands)

            result = {
                "dependencies": unique_deps,
                "build_commands": clean_commands,
                "build_system": build_system,
                "analyzed_files": [os.path.basename(f) for f in doc_files],
                "status": "success"
            }

            self.logger.append([
                "analyze_documents",
                target_path,
                f"Analyzed {len(doc_files)} files, found {len(unique_deps)} dependencies"
            ])

            return json.dumps(result)

        except Exception as e:
            return json.dumps({
                "dependencies": [],
                "build_commands": [],
                "build_system": "unknown",
                "status": "failed",
                "error": str(e)
            })

    def _discover_documentation_files(self, project_path: str) -> list:
        """发现项目中的文档文件"""
        doc_files = []

        try:
            for root, dirs, files in os.walk(project_path):
                # 限制搜索深度，避免过深的目录
                level = root.replace(project_path, '').count(os.sep)
                if level > 2:
                    continue

                for file in files:
                    file_lower = file.lower()
                    file_path = os.path.join(root, file)

                    # 检查是否是文档文件
                    is_doc = False

                    # 按关键词匹配
                    for keyword in self.compilation_keywords:
                        if keyword in file_lower:
                            is_doc = True
                            break

                    # 按扩展名匹配
                    if not is_doc:
                        for ext in self.document_extensions:
                            if file_lower.endswith(ext):
                                is_doc = True
                                break

                    if is_doc:
                        doc_files.append(file_path)

            # 按重要性排序
            doc_files.sort(key=self._get_file_priority)
            return doc_files[:10]  # 限制文件数量

        except Exception as e:
            logging.error(f"Error discovering documentation files: {e}")
            return []

    def _get_file_priority(self, file_path: str) -> int:
        """获取文件优先级，数字越小优先级越高"""
        filename = os.path.basename(file_path).lower()

        priority_map = {
            'readme': 1,
            'install': 2,
            'build': 3,
            'compile': 4,
            'setup': 5,
            'contributing': 6
        }

        for keyword, priority in priority_map.items():
            if keyword in filename:
                return priority

        return 10  # 默认优先级

    def _analyze_single_document(self, file_path: str) -> dict:
        """分析单个文档文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # 简化的文档分析 - 提取依赖和构建命令
            dependencies = self._extract_dependencies_from_text(content)
            build_commands = self._extract_build_commands_from_text(content)
            build_system = self._detect_build_system_from_text(content)

            return {
                "dependencies": dependencies,
                "build_commands": build_commands,
                "build_system": build_system,
                "file": file_path
            }

        except Exception as e:
            logging.error(f"Error analyzing document {file_path}: {e}")
            return None

    def _extract_dependencies_from_text(self, content: str) -> list:
        """从文本中提取依赖信息"""
        dependencies = []
        lines = content.lower().split('\n')

        # 常见的依赖关键词模式
        dep_patterns = [
            r'apt\s+install\s+([^\n]+)',
            r'apt-get\s+install\s+([^\n]+)',
            r'yum\s+install\s+([^\n]+)',
            r'brew\s+install\s+([^\n]+)',
            r'pip\s+install\s+([^\n]+)',
            r'requires?\s*:?\s*([^\n]+)',
            r'dependencies?\s*:?\s*([^\n]+)',
            r'depends?\s*:?\s*([^\n]+)'
        ]

        for line in lines:
            for pattern in dep_patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    # 清理和分割依赖包名
                    packages = re.split(r'[,\s]+', match.strip())
                    for pkg in packages:
                        pkg = pkg.strip()
                        # 改进的包名验证
                        if (pkg and len(pkg) > 2 and len(pkg) < 50 and
                            not pkg.startswith('-') and
                            not pkg.startswith('(') and
                            not pkg.endswith('.') and
                            not any(word in pkg.lower() for word in ['are', 'by', 'default', 'so', 'on', 'may', 'in', 'turn', 'depend', 'during', 'build', 'which', 'e.g.']) and
                            re.match(r'^[a-zA-Z][a-zA-Z0-9\-_+.]*$', pkg)):
                            dependencies.append({
                                "name": pkg,
                                "install_method": self._determine_install_method_from_pattern(pattern),
                                "confidence": 0.8,
                                "source": "document_analysis"
                            })

        return dependencies

    def _extract_build_commands_from_text(self, content: str) -> list:
        """从文本中提取构建命令"""
        build_commands = []
        lines = content.split('\n')

        # 寻找代码块或命令行示例
        in_code_block = False
        code_block_commands = []

        for line in lines:
            line_stripped = line.strip()

            # 检测代码块开始/结束
            if line_stripped.startswith('```') or line_stripped.startswith('~~~'):
                if in_code_block:
                    # 代码块结束，处理收集的命令
                    build_commands.extend(self._filter_build_commands(code_block_commands))
                    code_block_commands = []
                in_code_block = not in_code_block
                continue

            # 在代码块内收集命令
            if in_code_block:
                if line_stripped and not line_stripped.startswith('#'):
                    code_block_commands.append(line_stripped)
            else:
                # 检查是否是命令行格式
                if (line_stripped.startswith('$') or line_stripped.startswith('> ') or
                    any(cmd in line_stripped.lower() for cmd in ['make', 'cmake', 'configure', './configure'])):
                    clean_cmd = line_stripped.lstrip('$> ')
                    if clean_cmd:
                        build_commands.append(clean_cmd)

        return build_commands

    def _filter_build_commands(self, commands: list) -> list:
        """过滤和清理构建命令"""
        filtered = []
        build_keywords = ['make', 'cmake', 'configure', 'autogen', 'bootstrap', 'build']

        for cmd in commands:
            cmd_lower = cmd.lower()
            if any(keyword in cmd_lower for keyword in build_keywords):
                filtered.append(cmd)

        return filtered

    def _detect_build_system_from_text(self, content: str) -> str:
        """从文本内容检测构建系统"""
        content_lower = content.lower()

        if 'cmake' in content_lower or 'cmakelists.txt' in content_lower:
            return 'CMake'
        elif 'makefile' in content_lower or 'make ' in content_lower:
            return 'Make'
        elif 'configure' in content_lower or 'autotools' in content_lower:
            return 'Autotools'
        elif 'ninja' in content_lower:
            return 'Ninja'
        elif 'meson' in content_lower:
            return 'Meson'
        else:
            return 'unknown'

    def _determine_install_method_from_pattern(self, pattern: str) -> str:
        """根据模式确定安装方法"""
        if 'apt' in pattern:
            return 'apt'
        elif 'yum' in pattern:
            return 'yum'
        elif 'brew' in pattern:
            return 'brew'
        elif 'pip' in pattern:
            return 'pip'
        else:
            return 'apt'  # 默认

    def _deduplicate_dependencies(self, dependencies: list) -> list:
        """去重依赖列表"""
        seen = set()
        unique_deps = []

        for dep in dependencies:
            dep_name = dep.get('name', '').lower()
            if dep_name and dep_name not in seen:
                seen.add(dep_name)
                unique_deps.append(dep)

        return unique_deps

    def _clean_build_commands(self, commands: list) -> list:
        """清理和去重构建命令"""
        seen = set()
        clean_commands = []

        for cmd in commands:
            cmd_clean = cmd.strip()
            if cmd_clean and cmd_clean not in seen:
                seen.add(cmd_clean)
                clean_commands.append(cmd_clean)

        return clean_commands

    def detect_build_system(self, project_path: str = None) -> str:
        """
        Detect build system by examining project files.

        @param project_path: Path to project directory
        @return: JSON string with detected build system
        """
        target_path = project_path or self.project_path

        try:
            detected_systems = []

            for system, files in self.build_system_files.items():
                for filename in files:
                    file_path = os.path.join(target_path, filename)
                    if os.path.exists(file_path):
                        detected_systems.append(system)
                        break

            # 优先级排序
            priority = ['CMake', 'Autotools', 'Make', 'Ninja', 'Meson']
            for system in priority:
                if system in detected_systems:
                    return json.dumps({
                        "build_system": system,
                        "detected_systems": detected_systems,
                        "status": "success"
                    })

            return json.dumps({
                "build_system": "unknown",
                "detected_systems": detected_systems,
                "status": "no_build_system_found"
            })

        except Exception as e:
            return json.dumps({
                "build_system": "unknown",
                "detected_systems": [],
                "status": "failed",
                "error": str(e)
            })

    def debate_validate(self, analysis_result: str) -> str:
        """
        Validate analysis results using DEBATE methodology.

        @param analysis_result: JSON string with analysis results to validate
        @return: JSON string with validation results
        """
        try:
            data = json.loads(analysis_result)
            dependencies = data.get('dependencies', [])
            build_commands = data.get('build_commands', [])

            # 简单的验证逻辑
            validation_score = 0.0
            issues = []

            # 检查依赖的合理性
            if dependencies:
                validation_score += 0.4
            else:
                issues.append("No dependencies found")

            # 检查构建命令的合理性
            if build_commands:
                validation_score += 0.4
                # 检查命令的逻辑顺序
                has_configure = any('configure' in cmd.lower() for cmd in build_commands)
                has_make = any('make' in cmd.lower() for cmd in build_commands)
                if has_configure and has_make:
                    validation_score += 0.2
            else:
                issues.append("No build commands found")

            return json.dumps({
                "validation_score": validation_score,
                "issues": issues,
                "status": "success" if validation_score > 0.5 else "low_confidence"
            })

        except Exception as e:
            return json.dumps({
                "validation_score": 0.0,
                "issues": [f"Validation failed: {str(e)}"],
                "status": "failed"
            })


class GoogleSearchAgent:
    """Google搜索智能体 - 专门负责编译错误解决方案搜索"""

    def __init__(self, serper_api_key=None, proxy=None):
        self.serper_api_key = serper_api_key
        self.proxy = proxy
        self.logger = []

    def search_compilation_solutions(self, error_message: str, project_name: str = "") -> str:
        """
        Search Google for compilation error solutions.

        @param error_message: Compilation error message
        @param project_name: Name of the project (optional)
        @return: JSON string with search results
        """
        try:
            # 构建搜索查询
            search_query = self.construct_search_query(error_message, project_name)

            # 执行搜索
            if self.serper_api_key:
                results = self._search_with_serper(search_query)
            else:
                results = self._search_with_requests(search_query)

            # 提取解决方案
            solutions = self.extract_solutions_from_results(json.dumps(results))

            return solutions

        except Exception as e:
            return json.dumps({
                "solutions": [],
                "status": "failed",
                "error": str(e)
            })

    def construct_search_query(self, error_message: str, project_name: str = "") -> str:
        """
        Construct optimized search query for compilation errors.

        @param error_message: Raw error message
        @param project_name: Project name for context
        @return: Optimized search query string
        """
        # 提取关键错误信息
        key_terms = []

        # 提取错误类型
        error_patterns = [
            r'error:\s*([^:\n]+)',
            r'fatal error:\s*([^:\n]+)',
            r'undefined reference to\s*[`\'"]([^`\'"]+)[`\'"]',
            r'cannot find\s*([^\n]+)',
            r'no such file or directory:\s*([^\n]+)'
        ]

        for pattern in error_patterns:
            matches = re.findall(pattern, error_message, re.IGNORECASE)
            key_terms.extend(matches[:2])  # 限制数量

        # 构建查询
        query_parts = []

        if project_name:
            query_parts.append(f'"{project_name}"')

        query_parts.append("compilation error")

        for term in key_terms[:3]:  # 最多3个关键词
            term_clean = term.strip().replace('"', '')
            if term_clean and len(term_clean) > 3:
                query_parts.append(f'"{term_clean}"')

        query_parts.append("solution OR fix OR resolve")

        return " ".join(query_parts)

    def _search_with_serper(self, query: str) -> dict:
        """使用Serper API搜索"""
        url = "https://google.serper.dev/search"

        payload = {
            "q": query,
            "num": 10
        }

        headers = {
            "X-API-KEY": self.serper_api_key,
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers, timeout=30)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Serper API error: {response.status_code}")

    def _search_with_requests(self, query: str) -> dict:
        """使用requests直接搜索（备用方案）"""
        # 简化的搜索实现
        search_url = f"https://www.google.com/search?q={query}"

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        # 添加代理配置
        proxies = None
        if self.proxy:
            proxies = {
                "http": self.proxy,
                "https": self.proxy
            }

        try:
            response = requests.get(search_url, headers=headers, proxies=proxies, timeout=30)

            if response.status_code == 200:
                # 简单的结果解析
                soup = BeautifulSoup(response.text, 'html.parser')
                results = []

                for result in soup.find_all('div', class_='g')[:10]:
                    title_elem = result.find('h3')
                    link_elem = result.find('a')
                    snippet_elem = result.find('span', class_='st')

                    if title_elem and link_elem:
                        results.append({
                            'title': title_elem.get_text(),
                            'link': link_elem.get('href', ''),
                            'snippet': snippet_elem.get_text() if snippet_elem else ''
                        })

                return {'organic': results}
            else:
                return {'organic': []}

        except Exception as e:
            logging.error(f"Search error: {e}")
            return {'organic': []}

    def extract_solutions_from_results(self, search_results: str) -> str:
        """
        Extract actionable solutions from search results.

        @param search_results: JSON string with search results
        @return: JSON string with extracted solutions
        """
        try:
            data = json.loads(search_results)
            results = data.get('organic', [])

            solutions = []

            for result in results:
                title = result.get('title', '')
                snippet = result.get('snippet', '')
                link = result.get('link', '')

                # 分析标题和摘要，提取解决方案
                solution_text = f"{title} {snippet}".lower()

                # 寻找解决方案关键词
                solution_keywords = [
                    'install', 'apt-get', 'apt install', 'yum install',
                    'sudo', 'configure', 'cmake', 'make',
                    'export', 'set', 'path', 'library',
                    'fix', 'solution', 'resolve', 'solved'
                ]

                relevance_score = sum(1 for keyword in solution_keywords if keyword in solution_text)

                if relevance_score > 2:  # 相关性阈值
                    solutions.append({
                        'title': title,
                        'snippet': snippet,
                        'url': link,
                        'relevance_score': relevance_score,
                        'extracted_commands': self._extract_commands_from_text(snippet)
                    })

            # 按相关性排序
            solutions.sort(key=lambda x: x['relevance_score'], reverse=True)

            return json.dumps({
                'solutions': solutions[:5],  # 返回前5个最相关的
                'status': 'success',
                'total_found': len(solutions)
            })

        except Exception as e:
            return json.dumps({
                'solutions': [],
                'status': 'failed',
                'error': str(e)
            })

    def _extract_commands_from_text(self, text: str) -> list:
        """从文本中提取可能的命令"""
        commands = []

        # 常见命令模式
        command_patterns = [
            r'sudo\s+apt-get\s+install\s+[^\s]+',
            r'apt\s+install\s+[^\s]+',
            r'yum\s+install\s+[^\s]+',
            r'export\s+[A-Z_]+=\S+',
            r'cmake\s+[^\n]+',
            r'make\s+[^\n]*',
            r'./configure\s+[^\n]*'
        ]

        for pattern in command_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            commands.extend(matches)

        return commands[:3]  # 限制数量
