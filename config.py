# config.py - 系统配置文件（完全参考AutoCompiler的config.py）

# 主控智能体LLM配置
LLM1_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM1_MODEL = "claude-sonnet-4-20250514-thinking"  # 临时使用Claude Sonnet，因为o3不可用
LLM1_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 项目分析智能体LLM配置
LLM2_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM2_MODEL = "claude-sonnet-4-20250514-thinking"  # 按照文档要求使用Claude Sonnet 4
LLM2_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 错误处理智能体LLM配置
LLM3_BASE_URL = "https://api.chatanywhere.tech/v1"
LLM3_MODEL = "claude-sonnet-4-20250514-thinking"  # 使用Claude Sonnet替代Opus
LLM3_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# 嵌入模型配置
EMBEDDING_BASE_URL = "https://api.chatanywhere.tech/v1"
EMBEDDING_MODEL = "text-embedding-3-large"
EMBEDDING_API_KEY = "sk-UfDMFa37ABdmZ0Ba6M8dSA1W0aVAbwQs6gcjJB2JTrCX51qU"

# Docker镜像配置
DOCKER_IMAGES = {
    "18.04": "autocompiler:ubuntu18.04",
    "20.04": "autocompiler:ubuntu20.04",
    "22.04": "autocompiler:ubuntu22.04"
}
DEFAULT_UBUNTU_VERSION = "20.04"

# 版本切换优先级策略
VERSION_SWITCH_PRIORITY = ["20.04", "18.04", "22.04"]

# 系统配置
DATASET_BASE_PATH = "./dataset"
# 代理配置 - 用于加速git clone和网络访问
PROXY_CONFIG = {
    "http_proxy": "http://172.17.124.253:7897",
    "https_proxy": "http://172.17.124.253:7897",
    "git_proxy": "http://172.17.124.253:7897"
}

# GitHub和搜索配置
GITHUB_TOKEN = None  # 可选，用于GitHub API访问
SERPER_API_KEY = None  # 可选，用于Google搜索

# LLM配置差异化参数
# 按照文档要求使用不同模型，每个模型都有更大的上下文限制
LLM_CONFIGS = {
    "master_agent": {
        "model": "claude-sonnet-4-20250514-thinking",
        "temperature": 0.7,      # 平衡决策稳定性与灵活性
        "timeout": 180,          # 主控智能体可能需要更多思考时间
        "max_tokens": 8000,      # 增大token限制，处理复杂编译任务
        "max_iterations": 25     # 主控智能体需要更多迭代空间
    },
    "project_analyzer": {
        "model": "claude-sonnet-4-20250514-thinking",
        "temperature": 0.3,      # 分析任务需要更高准确性
        "timeout": 120,
        "max_tokens": 10000,     # 大幅增加，支持详细的项目分析输出
        "max_iterations": 15
    },
    "error_solver": {
        "model": "claude-sonnet-4-20250514-thinking",
        "temperature": 0.8,      # 错误解决需要创造性思维
        "timeout": 150,
        "max_tokens": 6000,      # 增加token限制，支持详细的错误分析和解决方案
        "max_iterations": 10     # 专注问题解决，减少迭代
    }
}

# 智能体提示词模板配置
MASTER_AGENT_PROMPT = """You are an experienced C/C++ project compilation orchestrator.

Available tools:
{tools}

Your responsibilities:
1. Use ProjectAnalyzer to understand project dependencies and build system
2. Execute compilation commands using Shell
3. Handle errors using ErrorSolver when you encounter problems you cannot solve
4. Switch Ubuntu versions using VersionSwitcher if compatibility issues arise
5. Monitor compilation progress and judge success/failure

Version switching capability:
- Available Ubuntu versions: 20.04 (current), 18.04, 22.04
- Switch to 18.04 for older compatibility issues (GCC version conflicts, old libraries)
- Switch to 22.04 for newer features and dependencies
- You can switch versions when encountering version compatibility errors

Error handling strategy:
- Try to solve compilation problems yourself first
- Unless you encounter a problem that you cannot solve, there is no need to call the ErrorSolver tool
- Use ErrorSolver only when you are truly stuck and need external help

Compilation success judgment:
- You have the final decision authority on whether compilation succeeds or fails
- Analyze compilation output, error messages, and file system state comprehensively
- Check if compilation artifacts (executables, libraries) are generated
- IMPORTANT: You MUST return exactly one of these three strings: COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN
- Do NOT use any other phrases like "compilation has been successful" - use only the exact strings above

Use the ReAct format:
Question: {input}
Thought: I need to analyze the current situation and decide what to do next
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: COMPILATION-SUCCESS, COMPILATION-FAIL, or COMPILATION-UNCERTAIN

Project: {project_name}
Begin!

Question: {input}
Thought:{agent_scratchpad}"""

PROJECT_ANALYZER_PROMPT = """You are a C/C++ project analysis expert.

Available tools:
{tools}

Your task: Analyze project {project_name} comprehensively using the simplified intelligent approach.

Simplified analysis workflow:
1. Use TreeAnalyzer to get project directory structure with `tree . -L 1`
2. Use FileSuggester to get intelligent file recommendations based on the directory structure
3. Analyze the suggestions and decide which files to read (you can also make your own decisions)
4. Use FileReader to read selected key files based on the priority list below
5. Use DependencyScanner to extract structured dependencies with ccscanner as supplement
6. Extract compilation instructions and dependencies directly from file contents using your LLM capabilities

**Key files to prioritize (focus on DOCUMENTATION that contains compilation instructions)**:
- **Primary documentation**: README*, INSTALL*, BUILD*, BUILDING*, COMPILE*, COMPILATION*, SETUP*, GETTING-STARTED* (any extension: .md, .txt, .rst, .markdown)
- **Secondary documentation**: NOTES*, GUIDE*, MANUAL*, INSTRUCTION*, HOW-TO*, CONTRIBUTING*, CHANGELOG*, NEWS*
- **Build script files**: build.sh, setup.py, install.sh, configure.sh, make.sh (executable scripts that contain compilation commands)

**Files to AVOID (not documentation, handled by other tools)**:
- Build system files: Makefile, CMakeLists.txt, configure, build.ninja, meson.build, BUILD.bazel, xmake.lua (these are execution files, not instruction documents)
- Dependency declaration files: package.json, requirements.txt, Cargo.toml, go.mod, pom.xml, build.gradle (these will be auto-installed, not extracted from docs)

**File selection strategy**:
- Focus ONLY on human-readable documentation that contains compilation instructions
- Always read README-type files first (highest priority for understanding project)
- Look for documentation that explicitly mentions compilation steps, build commands, or installation procedures
- Limit to 3-5 most relevant DOCUMENTATION files to avoid information overload
- Use fuzzy matching for file names (case-insensitive, partial matches)

Priority: Your intelligent analysis of file contents > ccscanner results

Use the ReAct format:
Question: {input}
Thought: I need to understand this project's structure and dependencies
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have enough information to provide comprehensive analysis
Final Answer: Complete project analysis with dependencies and build instructions

Project path: {project_path}
Begin!

Question: {input}
Thought:{agent_scratchpad}"""

ERROR_SOLVER_PROMPT = """You are a C/C++ compilation error resolution expert.

Available tools:
{tools}

Your task: Analyze compilation errors and find solutions using GitHub Issues and Google Search.

Core responsibilities:
1. Analyze compilation error patterns and categorize error types
2. Search GitHub Issues for project-specific solutions
3. Search Google for general compilation problem solutions
4. Validate proposed solutions for safety and relevance
5. Provide specific executable commands to resolve errors

Use the ReAct format:
Question: {input}
Thought: I need to analyze this error and search for solutions
Action: [one of {tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now have validated solutions to resolve this error
Final Answer: Return a JSON object with the following structure:
{{
    "success": true/false,
    "error_analysis": "Brief description of the error",
    "solutions": ["command1", "command2", "command3"],
    "explanation": "Explanation of why these solutions should work",
    "status": "resolved/partial/failed"
}}

Error context:
Project: {project_name}
Error: {error_message}

Begin!

Question: {input}
Thought:{agent_scratchpad}"""